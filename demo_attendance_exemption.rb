#!/usr/bin/env ruby

# Demo script to show attendance deduction exemption functionality
# Run with: rails runner demo_attendance_exemption.rb

puts "=== Attendance Deduction Exemption Demo ==="
puts

# Create two employees - one regular, one exempt
puts "1. Creating employees..."

regular_employee = Employee.new(
  user_id: 1001,
  department: :hr,
  start_date: Date.current,
  status: :active,
  phone: '+962790000000',
  exempt_from_attendance_deductions: false
)
regular_employee.save(validate: false)

exempt_employee = Employee.new(
  user_id: 1002,
  department: :hr,
  start_date: Date.current,
  status: :active,
  phone: '+962790000001',
  exempt_from_attendance_deductions: true
)
exempt_employee.save(validate: false)

puts "   Regular Employee ID: #{regular_employee.id} (exempt: #{regular_employee.exempt_from_attendance_deductions})"
puts "   Exempt Employee ID: #{exempt_employee.id} (exempt: #{exempt_employee.exempt_from_attendance_deductions})"
puts

# Create salary packages for both employees
puts "2. Creating salary packages..."

regular_package = SalaryPackage.create!(
  employee: regular_employee,
  base_salary: 1000,
  effective_date: Date.current.beginning_of_month
)

exempt_package = SalaryPackage.create!(
  employee: exempt_employee,
  base_salary: 1000,
  effective_date: Date.current.beginning_of_month
)

puts "   Salary packages created for both employees"
puts

# Simulate salary calculations
puts "3. Calculating salary deductions..."

# Create calculation service instances
regular_service = Salary::CalculationService.new(regular_employee, {
  start_date: Date.current.beginning_of_month,
  end_date: Date.current.end_of_month
})

exempt_service = Salary::CalculationService.new(exempt_employee, {
  start_date: Date.current.beginning_of_month,
  end_date: Date.current.end_of_month
})

# Calculate salaries
regular_calculation = regular_service.calculate
exempt_calculation = exempt_service.calculate

puts "   Regular Employee Calculation:"
puts "     Gross Salary: $#{regular_calculation.gross_salary}"
puts "     Attendance Deductions: $#{regular_calculation.deductions['attendance']}"
puts "     Net Salary: $#{regular_calculation.net_salary}"
puts

puts "   Exempt Employee Calculation:"
puts "     Gross Salary: $#{exempt_calculation.gross_salary}"
puts "     Attendance Deductions: $#{exempt_calculation.deductions['attendance']}"
puts "     Net Salary: $#{exempt_calculation.net_salary}"
puts

# Show calculation details
puts "4. Calculation Details:"
puts

puts "   Regular Employee Details:"
regular_calculation.calculation_details.each do |detail|
  puts "     #{detail.detail_type.titleize}: #{detail.category} - $#{detail.amount} (#{detail.description})"
end

puts
puts "   Exempt Employee Details:"
exempt_calculation.calculation_details.each do |detail|
  puts "     #{detail.detail_type.titleize}: #{detail.category} - $#{detail.amount} (#{detail.description})"
end

puts
puts "5. Testing scopes..."

exempt_employees = Employee.exempt_from_attendance_deductions
non_exempt_employees = Employee.not_exempt_from_attendance_deductions

puts "   Exempt employees count: #{exempt_employees.count}"
puts "   Non-exempt employees count: #{non_exempt_employees.count}"

puts
puts "=== Demo Complete ==="
puts "The attendance deduction exemption feature is working correctly!"
puts "Exempt employees have $0 attendance deductions, while regular employees have normal deductions."

# Cleanup
puts
puts "Cleaning up demo data..."
regular_calculation.destroy if regular_calculation.persisted?
exempt_calculation.destroy if exempt_calculation.persisted?
regular_package.destroy
exempt_package.destroy
regular_employee.destroy
exempt_employee.destroy
puts "Demo data cleaned up."
