namespace :attendance do
  desc "Sync attendance data from devices"
  task sync: :environment do
    start_date = ENV['START_DATE'] ? Date.parse(ENV['START_DATE']) : Date.yesterday
    end_date = ENV['END_DATE'] ? Date.parse(ENV['END_DATE']) : Date.today

    puts "Starting attendance sync from #{start_date} to #{end_date}..."

    # Initialize the integration service
    integration = Attendance::IntegrationService.new

    # Sync attendance data
    results = integration.sync(start_date, end_date)

    puts "Attendance sync completed: #{results[:success]} records imported, #{results[:failure]} failed"
  end

  desc "Generate device codes for employees"
  task generate_device_codes: :environment do
    puts "Generating device codes for employees without one..."

    count = 0
    Employee.where(device_code: nil).find_each do |employee|
      employee.ensure_device_code
      count += 1
    end

    puts "Generated device codes for #{count} employees"
  end

  desc "Import attendance data from a file"
  task :import_file, [:file_path] => :environment do |t, args|
    file_path = args[:file_path]

    unless file_path && File.exist?(file_path)
      puts "Error: File not found or not specified"
      puts "Usage: rake attendance:import_file[/path/to/file.csv]"
      exit 1
    end

    puts "Importing attendance data from #{file_path}..."

    # Initialize the file import adapter
    integration = Attendance::IntegrationService.new(:file_import, file_path: file_path)

    # Import the data
    results = integration.sync

    puts "Import completed: #{results[:success]} records imported, #{results[:failure]} failed"
  end

  desc "Test connection to a device"
  task :test_connection, [:ip, :port, :password] => :environment do |t, args|
    ip = args[:ip] || ENV['ZKTECO_DEVICE_IP']
    port = (args[:port] || ENV['ZKTECO_DEVICE_PORT'] || 4370).to_i
    password = (args[:password] || ENV['ZKTECO_DEVICE_PASSWORD'] || 0).to_i

    unless ip
      puts "Error: Device IP not specified"
      puts "Usage: rake attendance:test_connection[*************,4370,0]"
      exit 1
    end

    puts "Testing connection to device at #{ip}:#{port}..."

    adapter = Attendance::Adapters::ZKTecoAdapter.new(
      device_ip: ip,
      device_port: port,
      password: password
    )

    connected = adapter.test_connection

    if connected
      puts "✓ Successfully connected to device"

      # Get device information
      device_info = adapter.get_device_info
      if device_info.present?
        puts "\nDevice Information:"
        puts "  Firmware Version: #{device_info[:firmware_version]}"
        puts "  Platform: #{device_info[:platform]}"
        puts "  Device Name: #{device_info[:device_name]}"
        puts "  Serial Number: #{device_info[:serial_number]}"
        puts "  Users Count: #{device_info[:users_count]}"
        puts "  Records Count: #{device_info[:records_count]}"
      end
    else
      puts "✗ Failed to connect to device"
    end
  end

  desc "Create a new attendance device"
  task :create_device, [:name, :type, :ip, :port] => :environment do |t, args|
    name = args[:name]
    device_type = args[:type] || 'zkteco'
    ip = args[:ip]
    port = (args[:port] || 4370).to_i

    unless name
      puts "Error: Device name is required"
      puts "Usage: rake attendance:create_device[MyDevice,zkteco,*************,4370]"
      puts "       rake attendance:create_device[FileDevice,file_import]"
      exit 1
    end

    # IP is only required for network devices
    if device_type != 'file_import' && ip.blank?
      puts "Error: IP address is required for #{device_type} devices"
      exit 1
    end

    device = AttendanceDevice.new(
      name: name,
      device_type: device_type,
      ip_address: ip,
      port: port,
      connection_config: { timeout: 10, password: 0 },
      sync_config: { auto_sync_enabled: true, sync_interval: 30 }
    )

    if device.save
      puts "✓ Device '#{name}' created successfully (ID: #{device.id})"

      # Test connection
      puts "Testing connection..."
      if device.test_connection
        puts "✓ Connection test successful"
      else
        puts "⚠ Connection test failed"
      end
    else
      puts "✗ Failed to create device: #{device.errors.full_messages.join(', ')}"
    end
  end

  desc "List all attendance devices"
  task :list_devices => :environment do
    devices = AttendanceDevice.all

    if devices.empty?
      puts "No attendance devices found"
    else
      puts "\nAttendance Devices:"
      puts "=" * 80
      devices.each do |device|
        status_icon = case device.status
                      when 'active' then '✓'
                      when 'error' then '✗'
                      when 'maintenance' then '⚠'
                      else '○'
                      end

        puts "#{status_icon} #{device.name} (#{device.device_type})"
        puts "   IP: #{device.ip_address}:#{device.port}" if device.ip_address
        puts "   Status: #{device.status.humanize}"
        puts "   Last seen: #{device.last_seen_at || 'Never'}"
        puts "   Health score: #{device.sync_health_score}%"
        puts
      end
    end
  end

  desc "Sync a specific device"
  task :sync_device, [:device_id, :start_date, :end_date] => :environment do |t, args|
    device_id = args[:device_id]
    start_date = args[:start_date]&.to_date || Date.yesterday
    end_date = args[:end_date]&.to_date || Date.today

    unless device_id
      puts "Error: Device ID is required"
      puts "Usage: rake attendance:sync_device[1,2024-05-20,2024-05-24]"
      exit 1
    end

    device = AttendanceDevice.find(device_id)
    puts "Syncing device: #{device.name} for #{start_date} to #{end_date}"

    # Create sync log
    sync_log = AttendanceSyncLog.create_for_sync(
      device,
      :manual,
      { start_date: start_date, end_date: end_date }
    )

    # Perform sync
    job_id = ::DeviceSyncWorker.perform_async(device.id, sync_log.id)

    puts "Sync job queued with ID: #{job_id}"
    puts "Sync log ID: #{sync_log.id}"
  end

  desc "Health check for all devices"
  task :health_check => :environment do
    puts "Running health check for all devices..."

    job_id = Attendance::DeviceHealthMonitorWorker.perform_async
    puts "Health check job queued with ID: #{job_id}"

    # Show immediate basic status
    devices = AttendanceDevice.all
    puts "\nBasic Status:"
    puts "Total devices: #{devices.count}"
    puts "Active: #{devices.active.count}"
    puts "Error: #{devices.where(status: :error).count}"
    puts "Maintenance: #{devices.where(status: :maintenance).count}"
    puts "Inactive: #{devices.inactive.count}"
  end

  desc "Resolve undetermined attendance events for yesterday or a specified date"
  task :resolve_undetermined, [ :date ] => :environment do |t, args|
    # Use the provided date or default to yesterday
    target_date = args[:date].present? ? Date.parse(args[:date]) : Date.yesterday

    puts "Starting automatic resolution of undetermined attendance events for #{target_date}..."

    # Get employee count first to use in the block
    employee_count = AttendanceEvent.undetermined.for_date(target_date).pluck(:employee_id).uniq.count

    results = AttendanceResolutionService.resolve_for_date(target_date, employee_count) do |employee, index, resolved_for_employee, undetermined_after|
      puts "Processing employee #{index + 1}/#{employee_count}: #{employee.name} (ID: #{employee.id})"
      puts "  Resolved #{resolved_for_employee} events, #{undetermined_after} remain unresolved"
    end

    puts "Completed automatic resolution. Resolved #{results[:resolved_count]} events."

    # Report remaining undetermined events
    if results[:remaining_undetermined] > 0
      puts "WARNING: #{results[:remaining_undetermined]} events remain undetermined and require manual resolution."

      # You could add code here to send an email notification to HR staff
      # AttendanceMailer.undetermined_events_report(target_date, results[:remaining_undetermined]).deliver_now
    end
  end

  desc "Recalculate all attendance summaries for a specific date"
  task :recalculate_summaries, [ :date ] => :environment do |t, args|
    # Use the provided date or default to yesterday
    target_date = args[:date].present? ? Date.parse(args[:date]) : Date.yesterday

    puts "Recalculating attendance summaries for #{target_date}..."

    # Get all employees with attendance events for the target date
    employee_ids = AttendanceEvent.for_date(target_date).pluck(:employee_id).uniq

    employee_count = employee_ids.count

    employee_ids.each_with_index do |employee_id, index|
      employee = Employee.find(employee_id)

      puts "Processing employee #{index + 1}/#{employee_count}: #{employee.name} (ID: #{employee.id})"

      # Recalculate summary
      summary = ::Attendance::Summary.recalculate_for(employee, target_date)

      puts "  Summary updated: #{summary.work_status}, #{summary.total_duration_minutes} minutes"
    end

    puts "Completed recalculation of #{employee_count} attendance summaries."
  end
end
