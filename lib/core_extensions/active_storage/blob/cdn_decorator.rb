# https://devtut.github.io/ruby/design-patterns-and-idioms-in-ruby.html#decorator-pattern
# This module adds CDN URL support to Active Storage attachments
module CoreExtensions
  module ActiveStorage
    module Blob
      module CdnDecorator
        def cdn_url
          return url unless ENV['CDN_BASE_URL'].present?

          begin
            uri = URI.parse(url)
            "#{ENV['CDN_BASE_URL']}#{uri.path}#{uri.query ? "?#{uri.query}" : ""}"
          rescue URI::InvalidURIError => e
            Rails.logger.error "Failed to parse URL for CDN: #{e.message}" if defined?(Rails) && Rails.logger
            url
          end
        end
      end
    end
  end
end
