require 'rails_helper'

RSpec.describe Api::EmployeesController, type: :controller do
  let(:user) { OpenStruct.new(id: 1, name: 'Admin User', role_name: 'admin') }
  let(:current_employee) { OpenStruct.new(id: 999, user_id: 1) }

  before do
    # Mock authentication
    controller.stubs(:current_user).returns(user)
    controller.stubs(:current_employee).returns(current_employee)
    controller.stubs(:authenticate_session!).returns(true)
    controller.stubs(:authenticate_api_user!).returns(true)

    # Mock authorization
    controller.stubs(:authorize!).returns(true)
    controller.stubs(:can?).returns(true)

    # Mock specific permissions the controller checks
    controller.stubs(:can?).with(:create, :employee).returns(true)
    controller.stubs(:can?).with(:update, :employee).returns(true)
    controller.stubs(:can?).with(:update_own, :employee).returns(true)
    controller.stubs(:can?).with(:read, :employee).returns(true)
    controller.stubs(:can?).with(:read_own, :employee).returns(true)

    # Mock all Employee gRPC methods at the class level
    Employee.any_instance.stubs(:create_and_associate_user).returns(true)
    Employee.any_instance.stubs(:update_remote_user).returns(true)
    Employee.any_instance.stubs(:validate_remote_user).returns(true)
    Employee.any_instance.stubs(:load_user_data).returns(nil)
    Employee.any_instance.stubs(:sync_user_data).returns(true)
    Employee.any_instance.stubs(:update_user_record).returns(true)
    Employee.any_instance.stubs(:create_user_record).returns(true)
    Employee.any_instance.stubs(:active_rpc).returns(true)
    Employee.any_instance.stubs(:reload_user_data).returns(true)
  end

  describe 'POST #create' do
    let(:valid_attributes) do
      {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
        department: 'hr',
        start_date: Date.today,
        phone: '+962790000000'
      }
    end

    context 'with valid parameters' do
      it 'creates a new employee' do
        expect {
          post :create, params: { employee: valid_attributes }
        }.to change(Employee, :count).by(1)

        expect(response).to have_http_status(:created)
      end

      it 'creates a new employee with user_roles_list' do
        user_roles_list = [
          { role_id: '1', project_id: '1', is_default: true },
          { role_id: '2', project_id: '2' }
        ]

        post :create, params: { employee: valid_attributes.merge(user_roles_list: user_roles_list) }

        expect(response).to have_http_status(:created)
      end
    end

    context 'with invalid parameters' do
      it 'does not create a new employee' do
        # Invalid attributes (missing required fields)
        invalid_attributes = { name: 'John Doe' }

        expect {
          post :create, params: { employee: invalid_attributes }
        }.not_to change(Employee, :count)

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'PUT #update' do
    let!(:employee) { create(:employee) }

    before do
      # Mock the RPC client to avoid actual RPC calls
      Employee.any_instance.stubs(:update_user).returns(true)
    end

    it 'updates the employee with user_roles_list' do
      user_roles_list = [
        { role_id: '1', project_id: '1', is_default: true },
        { role_id: '2', project_id: '2' }
      ]

      put :update, params: { id: employee.id, employee: { user_roles_list: user_roles_list } }

      expect(response).to have_http_status(:ok)
    end
  end

  describe 'attendance deduction exemption' do
    describe 'parameter handling' do
      it 'includes exempt_from_attendance_deductions in create_params' do
        controller_instance = described_class.new
        params = ActionController::Parameters.new(
          employee: {
            name: 'Jane Doe',
            email: '<EMAIL>',
            department: 'hr',
            start_date: Date.today,
            phone: '+962790000000',
            exempt_from_attendance_deductions: true
          }
        )

        controller_instance.params = params
        permitted_params = controller_instance.send(:create_params)

        expect(permitted_params[:exempt_from_attendance_deductions]).to eq(true)
      end

      it 'includes exempt_from_attendance_deductions in update_params' do
        controller_instance = described_class.new
        params = ActionController::Parameters.new(
          employee: {
            exempt_from_attendance_deductions: true
          }
        )

        controller_instance.params = params
        permitted_params = controller_instance.send(:update_params)

        expect(permitted_params[:exempt_from_attendance_deductions]).to eq(true)
      end
    end
  end
end
