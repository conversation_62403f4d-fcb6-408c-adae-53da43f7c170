require 'rails_helper'

RSpec.describe Api::LeaveDocumentsController, type: :controller do
  let(:employee) { create(:employee) }
  let(:leave) do
    # Mock the approval callbacks to prevent throw(:abort)
    leave_instance = build(:leave, employee: employee)
    leave_instance.stubs(:submit_for_approval_if_pending).returns(true)
    leave_instance.stubs(:submit_for_approval).returns(true)
    leave_instance.save(validate: false)
    leave_instance
  end
  let(:test_file) do
    fixture_file_upload(
      Rails.root.join("spec/fixtures/files/test_attachment.pdf"),
      "application/pdf"
    )
  end
  let(:auth_token) { "valid_token" }

  before do
    # Set URL options for Active Storage
    ActiveStorage::Current.url_options = { host: "localhost", port: 3000, protocol: "http" }

    # Set up request headers
    request.headers['Authorization'] = "Bearer #{auth_token}"

    # Mock authentication
    mock_authenticate_session!

    # Mock authorization (both global and controller-specific)
    mock_authorize!(true)
    controller.stubs(:authorize!).returns(true)
    controller.stubs(:authenticate_api_user!).returns(true)
    controller.stubs(:current_employee).returns(employee)

    # Mock the specific permissions the controller checks
    controller.stubs(:can?).with(:read, :leave).returns(true)
    controller.stubs(:can?).with(:manage_own, :leave).returns(true)
    controller.stubs(:can?).with(:update, :leave).returns(true)

    # Mock the URL generation for Active Storage
    ActiveStorage::Blob.any_instance.stubs(:cdn_url).returns("http://example.com/test_attachment.pdf")
    ActiveStorage::Blob.any_instance.stubs(:url).returns("http://example.com/test_attachment.pdf")
  end

  describe "POST #create" do
    it "creates a document" do
      # Send the document request
      post :create, params: { leave_id: leave.id, document: test_file }

      # Verify that the document was added
      leave.reload
      expect(leave.documents.count).to eq(1)
    end
  end

  describe "GET #index" do
    it "lists documents" do
      # Add a document first
      leave.documents.attach(
        io: StringIO.new("test content"),
        filename: "test_file.pdf",
        content_type: "application/pdf"
      )

      # Mock the Apipie parameter validation to avoid errors
      controller.stubs(:apipie_validations).returns(true)

      # Create a relation for the documents
      documents_relation = ActiveStorage::Attachment.where(record: leave, name: 'documents')

      # Mock the apply_filters method to yield and return the filtered result
      controller.stubs(:apply_filters)
                .with(anything)
                .yields(documents_relation)
                .returns(documents_relation)

      # Mock the paginate method to return the documents and metadata
      controller.stubs(:paginate)
                .with(documents_relation)
                .returns([documents_relation, { total: 1 }])

      # Send the index request
      get :index, params: { leave_id: leave.id }

      # Verify the response
      expect(response).to have_http_status(:ok)
    end
  end

  describe "GET #show" do
    it "shows a document" do
      # Add a document first
      document = leave.documents.attach(
        io: StringIO.new("test content"),
        filename: "test_file.pdf",
        content_type: "application/pdf"
      ).first

      # Send the show request
      get :show, params: { leave_id: leave.id, id: document.id }

      # Verify the response
      expect(response).to have_http_status(:ok)
    end
  end

  describe "DELETE #destroy" do
    it "deletes a document" do
      # Add a document first
      document = leave.documents.attach(
        io: StringIO.new("test content"),
        filename: "test_file.pdf",
        content_type: "application/pdf"
      ).first

      # Send the delete request
      delete :destroy, params: { leave_id: leave.id, id: document.id }

      # Verify that the response is successful
      expect(response).to have_http_status(:no_content)

      # Verify that the document was deleted
      leave.reload
      expect(leave.documents.count).to eq(0)
    end
  end

  describe "PUT #update" do
    it "renames a document" do
      # Add a document first
      document = leave.documents.attach(
        io: StringIO.new("test content"),
        filename: "test_file.pdf",
        content_type: "application/pdf"
      ).first

      # Mock the blob update
      ActiveStorage::Blob.any_instance.stubs(:update).returns(true)

      # Send the update request
      put :update, params: {
        leave_id: leave.id,
        id: document.id,
        document: { filename: "renamed_file" }
      }

      # Verify the response
      expect(response).to have_http_status(:ok)
    end
  end
end
