require 'rails_helper'

RSpec.describe Api::StatisticsController, type: :controller do
  before do
    # Mock authentication
    controller.stubs(:authenticate_session!).returns(true)
    controller.stubs(:authenticate_api_user!).returns(true)
    controller.stubs(:authorize!).returns(true)

    # Mock authentication status
    controller.stubs(:authenticated?).returns(true)

    # Mock current_employee
    @employee = mock('Employee')
    @employee.stubs(:id).returns(1)
    controller.stubs(:current_employee).returns(@employee)

    # Mock Employee.find
    Employee.stubs(:find).returns(@employee)

    # Mock can? method
    controller.stubs(:can?).returns(true)

    # Mock metric card service
    @mock_service = mock('MetricCardService')
    Statistics::MetricCardService.stubs(:new).returns(@mock_service)

    # Mock metric cards
    @mock_cards = [
      Statistics::MetricCard.new(
        id: 'leaves',
        title: 'Leaves Taken',
        value: '5',
        unit: 'days',
        comparison_percentage: 25.0,
        trend: 'up',
        comparison_text: 'compared to last month'
      ),
      Statistics::MetricCard.new(
        id: 'late_arrivals',
        title: 'Late Arrivals',
        value: '3',
        unit: 'times',
        comparison_percentage: -50.0,
        trend: 'down',
        comparison_text: 'compared to last month'
      )
    ]
  end

  describe "GET #index" do
    it "returns all cards when no filter is specified" do
      # Expect the service to be called with nil for card_types
      @mock_service.expects(:get_cards).with(instance_of(Hash), nil).returns(@mock_cards)

      get :index
      expect(response).to have_http_status(:success)

      # Parse the response
      json = JSON.parse(response.body)
      expect(json["data"].length).to eq(2)
    end

    it "filters cards using the JSON:API format" do
      # Expect the service to be called with the specified card types
      @mock_service.expects(:get_cards).with(instance_of(Hash), [ :late_arrivals ]).returns([ @mock_cards.last ])

      get :index, params: { filter: { metric_key_in: [ 'late_arrivals' ] } }
      expect(response).to have_http_status(:success)

      # Parse the response
      json = JSON.parse(response.body)
      expect(json["data"].length).to eq(1)
      expect(json["data"][0]["id"]).to eq('late_arrivals')
    end

    it "handles multiple card types in the JSON:API format" do
      # Expect the service to be called with multiple card types
      @mock_service.expects(:get_cards).with(instance_of(Hash), [ :leaves, :late_arrivals ]).returns(@mock_cards)

      get :index, params: { filter: { metric_key_in: [ 'leaves', 'late_arrivals' ] } }
      expect(response).to have_http_status(:success)

      # Parse the response
      json = JSON.parse(response.body)
      expect(json["data"].length).to eq(2)
    end

    it "passes context parameter" do
      # Expect the service to be called with the context parameter
      expected_context = { employee_id: '1', custom_param: 'custom_value' }
      @mock_service.expects(:get_cards).with(expected_context, anything).returns(@mock_cards)

      get :index, params: { context: expected_context }
      expect(response).to have_http_status(:success)
    end

    it "handles nested context parameters" do
      # Expect the service to be called with the extracted context
      @mock_service.expects(:get_cards).with(
        { employee_id: '1', start_date: '2025-01-01', end_date: '2025-01-31' },
        anything
      ).returns(@mock_cards)

      # Simulate how the frontend would send nested parameters
      get :index, params: {
        context: {
          employee_id: '1',
          start_date: '2025-01-01',
          end_date: '2025-01-31'
        }
      }
      expect(response).to have_http_status(:success)
    end
  end
end
