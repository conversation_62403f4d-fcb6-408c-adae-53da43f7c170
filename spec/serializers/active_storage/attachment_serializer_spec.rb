require 'rails_helper'

RSpec.describe ActiveStorage::AttachmentSerializer, type: :serializer do
  describe 'serialization' do
    it 'serializes attachment attributes' do
      # Create an employee
      employee = build(:employee)
      
      # Mock an attachment and its blob
      attachment = mock('ActiveStorage::Attachment')
      blob = mock('ActiveStorage::Blob')
      
      # Set up the attachment mock
      attachment.stubs(:id).returns(1)
      attachment.stubs(:filename).returns('document.pdf')
      attachment.stubs(:content_type).returns('application/pdf')
      attachment.stubs(:created_at).returns(Time.new(2022, 1, 12, 8, 0, 0))
      attachment.stubs(:blob).returns(blob)
      attachment.stubs(:cdn_url).returns('https://cdn.example.com/document.pdf')
      attachment.stubs(:url).returns('https://storage.example.com/document.pdf')
      
      # Set up the blob mock
      blob.stubs(:byte_size).returns(1024 * 1024) # 1MB
      blob.stubs(:present?).returns(true)
      
      # Serialize the attachment
      serializer = described_class.new(attachment)
      json = serializer.serializable_hash
      
      # Verify the serialized data
      expect(json[:data][:id]).to eq('1')
      expect(json[:data][:type]).to eq(:attachment)
      
      # Verify attributes
      attributes = json[:data][:attributes]
      expect(attributes[:filename]).to eq('document.pdf')
      expect(attributes[:content_type]).to eq('application/pdf')
      expect(attributes[:created_at]).to eq(attachment.created_at)
      expect(attributes[:url]).to eq('https://cdn.example.com/document.pdf')
      expect(attributes[:bytes_size]).to eq(1024 * 1024)
    end
    
    it 'falls back to url when cdn_url is not available' do
      # Create an employee
      employee = build(:employee)
      
      # Mock an attachment and its blob
      attachment = mock('ActiveStorage::Attachment')
      blob = mock('ActiveStorage::Blob')
      
      # Set up the attachment mock
      attachment.stubs(:id).returns(1)
      attachment.stubs(:filename).returns('document.pdf')
      attachment.stubs(:content_type).returns('application/pdf')
      attachment.stubs(:created_at).returns(Time.new(2022, 1, 12, 8, 0, 0))
      attachment.stubs(:blob).returns(blob)
      attachment.stubs(:cdn_url).returns(nil)
      attachment.stubs(:url).returns('https://storage.example.com/document.pdf')
      
      # Set up the blob mock
      blob.stubs(:byte_size).returns(1024 * 1024) # 1MB
      blob.stubs(:present?).returns(true)
      
      # Serialize the attachment
      serializer = described_class.new(attachment)
      json = serializer.serializable_hash
      
      # Verify the url attribute falls back to url
      attributes = json[:data][:attributes]
      expect(attributes[:url]).to eq('https://storage.example.com/document.pdf')
    end
    
    it 'handles missing blob gracefully' do
      # Create an employee
      employee = build(:employee)
      
      # Mock an attachment without a blob
      attachment = mock('ActiveStorage::Attachment')
      
      # Set up the attachment mock
      attachment.stubs(:id).returns(1)
      attachment.stubs(:filename).returns('document.pdf')
      attachment.stubs(:content_type).returns('application/pdf')
      attachment.stubs(:created_at).returns(Time.new(2022, 1, 12, 8, 0, 0))
      attachment.stubs(:blob).returns(nil)
      attachment.stubs(:cdn_url).returns(nil)
      attachment.stubs(:url).returns('https://storage.example.com/document.pdf')
      
      # Serialize the attachment
      serializer = described_class.new(attachment)
      json = serializer.serializable_hash
      
      # Verify the bytes_size attribute is nil
      attributes = json[:data][:attributes]
      expect(attributes[:bytes_size]).to be_nil
    end
  end
end
