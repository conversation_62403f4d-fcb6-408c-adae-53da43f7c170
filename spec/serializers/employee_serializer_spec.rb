require 'rails_helper'

RSpec.describe EmployeeSerializer, type: :serializer do
  describe 'serialization' do
    it 'serializes basic employee attributes' do
      # Create an employee with test data
      employee = build(:employee,
        id: 1,
        user_id: 12345,
        department: :hr,
        start_date: Date.new(2023, 1, 15),
        status: :active,
        phone: '+962790123456'
      )

      # Mock the user data methods
      employee.stubs(:name).returns('<PERSON>')
      employee.stubs(:email).returns('<EMAIL>')
      employee.stubs(:avatar_url).returns('https://example.com/avatar.jpg')
      employee.stubs(:permissions).returns(['read:employee', 'update:employee'])
      employee.stubs(:department_name).returns('Human Resources')
      employee.stubs(:phone_intl).returns('+962 79 012 3456')
      employee.stubs(:user_roles_list).returns([
        {
          'role' => {
            'id' => 1,
            'name' => 'HR Manager',
            'global' => true
          },
          'project' => nil
        },
        {
          'role' => {
            'id' => 2,
            'name' => 'Team Lead',
            'global' => false
          },
          'project' => {
            'id' => 100,
            'name' => 'Project Alpha'
          }
        }
      ])

      # Serialize the employee
      serializer = described_class.new(employee)
      json = serializer.serializable_hash

      # Verify the serialized data
      expect(json[:data][:id]).to eq(employee.id.to_s)
      expect(json[:data][:type]).to eq(:employee)

      # Verify attributes
      attributes = json[:data][:attributes]
      expect(attributes[:name]).to eq('John Doe')
      expect(attributes[:email]).to eq('<EMAIL>')
      expect(attributes[:user_id]).to eq(12345)
      expect(attributes[:department]).to eq('hr')
      expect(attributes[:department_name]).to eq('Human Resources')
      expect(attributes[:start_date].to_s).to eq('2023-01-15')
      expect(attributes[:status]).to eq('active')
      expect(attributes[:phone]).to eq('07 9012 3456')
      expect(attributes[:phone_intl]).to eq('+962 79 012 3456')
      expect(attributes[:avatar_url]).to eq('https://example.com/avatar.jpg')
      expect(attributes[:permissions]).to eq(['read:employee', 'update:employee'])

      # Verify user_roles_list (if it exists)
      user_roles_list = attributes[:user_roles_list]
      if user_roles_list
        expect(user_roles_list.length).to eq(2)
      end

      # Verify role details (if user_roles_list exists and has content)
      if user_roles_list && user_roles_list.length >= 2
        # Verify first role (global role without project)
        expect(user_roles_list[0][:role][:id]).to eq(1)
        expect(user_roles_list[0][:role][:name]).to eq('HR Manager')
        expect(user_roles_list[0][:role][:global]).to eq(true)
        expect(user_roles_list[0][:project]).to be_nil

        # Verify second role (project-specific role)
        expect(user_roles_list[1][:role][:id]).to eq(2)
        expect(user_roles_list[1][:role][:name]).to eq('Team Lead')
        expect(user_roles_list[1][:role][:global]).to eq(false)
        expect(user_roles_list[1][:project][:id]).to eq(100)
        expect(user_roles_list[1][:project][:name]).to eq('Project Alpha')
      end
    end

    it 'handles nil user_roles_list gracefully' do
      # Create an employee with test data
      employee = build(:employee)

      # Mock the user data methods with nil user_roles_list
      employee.stubs(:name).returns('Jane Smith')
      employee.stubs(:email).returns('<EMAIL>')
      employee.stubs(:user_roles_list).returns(nil)

      # Serialize the employee
      serializer = described_class.new(employee)
      json = serializer.serializable_hash

      # Verify the serialized data
      attributes = json[:data][:attributes]
      expect(attributes[:user_roles_list]).to be_nil
    end
  end
end
