require 'rails_helper'

RSpec.describe Attendance::EventSerializer, type: :serializer do
  describe 'serialization' do
    it 'serializes basic attendance event attributes' do
      # Create an employee
      employee = build(:employee, id: 1)

      # Create an attendance event
      event = Attendance::Event.new(
        id: 1,
        employee: employee,
        timestamp: 1641981600, # 2022-01-12 08:00:00 UTC
        event_type: :check_in,
        activity_type: :regular,
        location: 'Office',
        notes: 'Morning check-in',
        created_at: Time.new(2022, 1, 12, 8, 0, 0),
        updated_at: Time.new(2022, 1, 12, 8, 0, 0)
      )

      # Serialize the event
      serializer = described_class.new(event)
      json = serializer.serializable_hash

      # Verify the serialized data
      expect(json[:data][:id]).to eq(event.id.to_s)
      expect(json[:data][:type]).to eq(:event)

      # Verify attributes
      attributes = json[:data][:attributes]
      expect(attributes[:timestamp]).to eq(1641981600)
      expect(attributes[:event_type]).to eq('check_in')
      expect(attributes[:activity_type]).to eq('regular')
      expect(attributes[:location]).to eq('Office')
      expect(attributes[:notes]).to eq('Morning check-in')
      expect(attributes[:created_at]).to eq(event.created_at)
      expect(attributes[:updated_at]).to eq(event.updated_at)

      # Verify relationships
      relationships = json[:data][:relationships]
      expect(relationships[:employee][:data][:id]).to eq(employee.id.to_s)
      expect(relationships[:employee][:data][:type]).to eq(:employee)
    end

    it 'includes inferred_type when requested' do
      # Create an employee
      employee = build(:employee)

      # Create an undetermined event
      event = Attendance::Event.new(
        employee: employee,
        timestamp: 1641981600, # 2022-01-12 08:00:00 UTC
        event_type: :undetermined,
        activity_type: :regular
      )

      # Mock the inference methods
      event.stubs(:infer_event_type_by_time).returns('check_in')
      event.stubs(:infer_event_type_by_sequence).returns('check_in')
      event.stubs(:undetermined?).returns(true)

      # Serialize the event with include_inference parameter
      serializer = described_class.new(event, { params: { include_inference: true } })
      json = serializer.serializable_hash

      # Verify the inferred_type attribute
      attributes = json[:data][:attributes]
      expect(attributes[:inferred_type]).to eq('check_in')
    end

    it 'handles conflicting inferences correctly' do
      # Create an employee
      employee = build(:employee)

      # Create an undetermined event
      event = Attendance::Event.new(
        employee: employee,
        timestamp: 1641981600, # 2022-01-12 08:00:00 UTC
        event_type: :undetermined,
        activity_type: :regular
      )

      # Mock the inference methods with conflicting results
      event.stubs(:infer_event_type_by_time).returns('check_in')
      event.stubs(:infer_event_type_by_sequence).returns('check_out')
      event.stubs(:undetermined?).returns(true)

      # Serialize the event with include_inference parameter
      serializer = described_class.new(event, { params: { include_inference: true } })
      json = serializer.serializable_hash

      # Verify the inferred_type attribute (should prefer sequence-based inference)
      attributes = json[:data][:attributes]
      expect(attributes[:inferred_type]).to eq('check_out')
    end

    it 'returns nil for inferred_type when not requested' do
      # Create an employee
      employee = build(:employee)

      # Create an undetermined event
      event = Attendance::Event.new(
        employee: employee,
        timestamp: 1641981600, # 2022-01-12 08:00:00 UTC
        event_type: :undetermined,
        activity_type: :regular
      )

      # Serialize the event without include_inference parameter
      serializer = described_class.new(event)
      json = serializer.serializable_hash

      # Verify the inferred_type attribute is nil
      attributes = json[:data][:attributes]
      expect(attributes[:inferred_type]).to be_nil
    end
  end
end
