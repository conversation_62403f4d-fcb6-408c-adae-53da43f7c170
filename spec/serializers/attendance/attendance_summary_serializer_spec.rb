require 'rails_helper'

RSpec.describe Attendance::SummarySerializer, type: :serializer do
  describe 'serialization' do
    it 'serializes basic attendance summary attributes' do
      # Create an employee
      employee = build(:employee)

      # Create an attendance summary
      summary = Attendance::Summary.new(
        id: 1,
        employee: employee,
        date: Date.new(2022, 1, 12),
        first_check_in: Time.at(1641981600),  # 2022-01-12 08:00:00 UTC
        last_check_out: Time.at(1642010400),  # 2022-01-12 16:00:00 UTC
        total_duration_minutes: 480, # 8 hours
        work_status: :complete,
        event_count: 4,
        undetermined_count: 0,
        notes: 'Regular workday',
        created_at: Time.new(2022, 1, 12, 16, 5, 0),
        updated_at: Time.new(2022, 1, 12, 16, 5, 0)
      )

      # Serialize the summary
      serializer = described_class.new(summary)
      json = serializer.serializable_hash

      # Verify the serialized data
      expect(json[:data][:id]).to eq(summary.id.to_s)
      expect(json[:data][:type]).to eq(:summary)

      # Verify attributes
      attributes = json[:data][:attributes]
      expect(attributes[:date].to_s).to eq('2022-01-12')
      expect(attributes[:first_check_in]).to eq(Time.at(1641981600).utc)
      expect(attributes[:last_check_out]).to eq(Time.at(1642010400).utc)
      expect(attributes[:total_duration_minutes]).to eq(480)
      expect(attributes[:work_status]).to eq('complete')
      expect(attributes[:event_count]).to eq(4)
      expect(attributes[:undetermined_count]).to eq(0)
      expect(attributes[:notes]).to eq('Regular workday')
      expect(attributes[:created_at]).to eq(summary.created_at)
      expect(attributes[:updated_at]).to eq(summary.updated_at)

      # Verify computed attributes
      expect(attributes[:total_duration_hours]).to eq(8.0)

      # Note: Relationships may not be included in this serializer
    end

    it 'calculates total_duration_hours correctly for non-integer hours' do
      # Create an employee
      employee = build(:employee)

      # Create an attendance summary with non-integer hours
      summary = Attendance::Summary.new(
        employee: employee,
        date: Date.new(2022, 1, 13),
        first_check_in: Time.at(1642068000),  # 2022-01-13 08:00:00 UTC
        last_check_out: Time.at(1642093200),  # 2022-01-13 15:00:00 UTC
        total_duration_minutes: 420, # 7 hours
        work_status: :incomplete,
        event_count: 2,
        undetermined_count: 0
      )

      # Serialize the summary
      serializer = described_class.new(summary)
      json = serializer.serializable_hash

      # Verify the total_duration_hours attribute
      attributes = json[:data][:attributes]
      expect(attributes[:total_duration_hours]).to eq(7.0)
    end

    it 'handles partial hours correctly' do
      # Create an employee
      employee = build(:employee)

      # Create an attendance summary with partial hours
      summary = Attendance::Summary.new(
        employee: employee,
        date: Date.new(2022, 1, 14),
        first_check_in: Time.at(1642154400),  # 2022-01-14 08:00:00 UTC
        last_check_out: Time.at(1642176000),  # 2022-01-14 14:00:00 UTC
        total_duration_minutes: 330, # 5.5 hours
        work_status: :incomplete,
        event_count: 2,
        undetermined_count: 0
      )

      # Serialize the summary
      serializer = described_class.new(summary)
      json = serializer.serializable_hash

      # Verify the total_duration_hours attribute
      attributes = json[:data][:attributes]
      expect(attributes[:total_duration_hours]).to eq(5.5)
    end
  end
end
