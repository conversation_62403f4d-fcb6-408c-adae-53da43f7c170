require 'rails_helper'

RSpec.describe LeaveSerializer, type: :serializer do
  describe 'serialization' do
    it 'serializes basic leave attributes' do
      # Create an employee
      employee = build(:employee, id: 1)

      # Create a leave record
      leave = Leave.new(
        id: 1,
        employee: employee,
        leave_type: :annual,
        leave_duration: :full_day,
        status: :pending,
        start_date: Date.new(2022, 7, 1),
        end_date: Date.new(2022, 7, 5),
        reason: 'Summer vacation',
        created_at: Time.new(2022, 6, 15, 10, 0, 0),
        updated_at: Time.new(2022, 6, 15, 10, 0, 0)
      )

      # Mock the private methods
      leave.stubs(:duration).returns(5)
      leave.stubs(:working_days).returns(3)
      leave.stubs(:with_deduction?).returns(true)

      # Serialize the leave
      serializer = described_class.new(leave)
      json = serializer.serializable_hash

      # Verify the serialized data
      expect(json[:data][:id]).to eq(leave.id.to_s)
      expect(json[:data][:type]).to eq(:leave)

      # Verify attributes
      attributes = json[:data][:attributes]
      expect(attributes[:leave_type]).to eq('annual')
      expect(attributes[:leave_duration]).to eq('full_day')
      expect(attributes[:status]).to eq('pending')
      expect(attributes[:start_date].to_s).to eq('2022-07-01')
      expect(attributes[:end_date].to_s).to eq('2022-07-05')
      expect(attributes[:reason]).to eq('Summer vacation')
      expect(attributes[:created_at]).to eq(leave.created_at)
      expect(attributes[:updated_at]).to eq(leave.updated_at)

      # Verify computed attributes
      expect(attributes[:duration]).to eq(5)
      expect(attributes[:working_days]).to eq(3)
      expect(attributes[:with_deduction]).to eq(true)

      # Verify relationships
      relationships = json[:data][:relationships]
      expect(relationships[:employee][:data][:id]).to eq(employee.id.to_s)
      expect(relationships[:employee][:data][:type]).to eq(:employee)
    end

    it 'handles half-day leave correctly' do
      # Create an employee
      employee = build(:employee, id: 2)

      # Create a half-day leave record
      leave = Leave.new(
        id: 2,
        employee: employee,
        leave_type: :sick,
        leave_duration: :half_day_morning,
        status: :approved,
        start_date: Date.new(2022, 8, 10),
        end_date: Date.new(2022, 8, 10),
        reason: 'Doctor appointment'
      )

      # Mock the private methods
      leave.stubs(:duration).returns(0.5)
      leave.stubs(:working_days).returns(0.5)
      leave.stubs(:with_deduction?).returns(false)

      # Serialize the leave
      serializer = described_class.new(leave)
      json = serializer.serializable_hash

      # Verify attributes
      attributes = json[:data][:attributes]
      expect(attributes[:leave_duration]).to eq('half_day_morning')
      expect(attributes[:duration]).to eq(0.5)
      expect(attributes[:working_days]).to eq(0.5)
      expect(attributes[:with_deduction]).to eq(false)
    end
  end
end
