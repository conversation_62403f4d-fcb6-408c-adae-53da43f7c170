require 'rails_helper'

RSpec.describe 'Attendance Deduction Exemption Integration', type: :integration do
  describe 'Employee exemption functionality' do
    let(:regular_employee) { build(:employee, exempt_from_attendance_deductions: false) }
    let(:exempt_employee) { build(:employee, exempt_from_attendance_deductions: true) }

    it 'allows creating employees with exemption status' do
      # Test creating a regular employee
      regular_employee.save(validate: false)
      expect(regular_employee.exempt_from_attendance_deductions).to be false

      # Test creating an exempt employee
      exempt_employee.save(validate: false)
      expect(exempt_employee.exempt_from_attendance_deductions).to be true
    end

    it 'includes exemption status in serialization' do
      regular_employee.save(validate: false)
      
      serializer = EmployeeSerializer.new(regular_employee)
      json = serializer.serializable_hash
      
      expect(json[:data][:attributes]).to have_key(:exempt_from_attendance_deductions)
      expect(json[:data][:attributes][:exempt_from_attendance_deductions]).to be false
    end

    it 'applies exemption in salary calculations' do
      # Create a test service that includes the attendance integration
      service_class = Class.new do
        include Salary::AttendanceIntegration
        
        attr_accessor :employee
        
        def initialize(employee)
          @employee = employee
        end
        
        def calculate_working_days(start_date, end_date)
          (start_date..end_date).count { |date| (1..5).include?(date.wday) }
        end
        
        def count_approved_leave_days(start_date, end_date)
          0
        end
        
        def calculate_late_arrivals_deduction(start_date, end_date, gross_salary)
          100
        end
        
        def calculate_early_departures_deduction(start_date, end_date, gross_salary)
          50
        end
      end

      # Test with regular employee
      regular_service = service_class.new(regular_employee)
      regular_calculation = SalaryCalculation.new(
        employee: regular_employee,
        period_start_date: Date.current.beginning_of_month,
        period_end_date: Date.current.end_of_month,
        gross_salary: 1000
      )
      
      regular_result = regular_service.calculate_attendance_deductions(regular_calculation)
      expect(regular_result).to be > 0
      expect(regular_calculation.calculation_details.any? { |d| d.category == 'attendance_exempt' }).to be false

      # Test with exempt employee
      exempt_service = service_class.new(exempt_employee)
      exempt_calculation = SalaryCalculation.new(
        employee: exempt_employee,
        period_start_date: Date.current.beginning_of_month,
        period_end_date: Date.current.end_of_month,
        gross_salary: 1000
      )
      
      exempt_result = exempt_service.calculate_attendance_deductions(exempt_calculation)
      expect(exempt_result).to eq(0)
      expect(exempt_calculation.calculation_details.any? { |d| d.category == 'attendance_exempt' }).to be true
    end
  end

  describe 'Database constraints' do
    it 'has proper default value' do
      employee = Employee.new
      expect(employee.exempt_from_attendance_deductions).to be false
    end

    it 'validates inclusion of boolean values' do
      employee = Employee.new(
        user_id: 999,
        department: :hr,
        start_date: Date.today,
        status: :active,
        phone: '+962790000000'
      )
      
      # Test valid values
      employee.exempt_from_attendance_deductions = true
      expect(employee.valid?).to be true
      
      employee.exempt_from_attendance_deductions = false
      expect(employee.valid?).to be true
      
      # Test invalid value
      employee.exempt_from_attendance_deductions = nil
      expect(employee.valid?).to be false
      expect(employee.errors[:exempt_from_attendance_deductions]).to include('is not included in the list')
    end
  end
end
