require 'rails_helper'

RSpec.describe "Attendance System Integration", type: :integration do
  # Mock all external dependencies to prevent actual network calls
  before do
    # Mock Employee gRPC methods
    Employee.any_instance.stubs(:update_remote_user).returns(true)
    Employee.any_instance.stubs(:validate_remote_user).returns(true)
    Employee.any_instance.stubs(:load_user_data).returns(nil)
    Employee.any_instance.stubs(:sync_user_data).returns(true)
    Employee.any_instance.stubs(:update_user_record).returns(true)
    Employee.any_instance.stubs(:create_user_record).returns(true)
    Employee.any_instance.stubs(:active_rpc).returns(true)
    Employee.any_instance.stubs(:reload_user_data).returns(true)

    # Mock Sidekiq for background jobs
    Sidekiq::Testing.fake!
  end

  after do
    Sidekiq::Queues.clear_all
  end

  describe "Device Management" do
    let!(:zkteco_device) do
      create(:attendance_device,
        name: "Test ZKTeco Device",
        adapter_type: :zkteco,
        ip_address: "***************",
        port: 4370,
        status: :active
      )
    end

    let!(:generic_device) do
      create(:attendance_device,
        name: "Test Generic Device",
        adapter_type: :generic_http,
        ip_address: "*************",
        status: :active
      )
    end

    it "manages attendance devices successfully" do
      expect(Attendance::Device.count).to eq(2)
      expect(Attendance::Device.active.count).to eq(2)

      devices = Attendance::Device.all
      expect(devices.map(&:name)).to include("Test ZKTeco Device", "Test Generic Device")
      expect(devices.map(&:adapter_type)).to include("zkteco", "generic_http")
    end

    it "provides device status information" do
      devices = Attendance::Device.all

      devices.each do |device|
        expect(device.status).to be_present
        expect(device.name).to be_present
        expect(device.adapter_type).to be_present
      end
    end
  end

  describe "Adapter Functionality" do
    let!(:zkteco_device) do
      create(:attendance_device,
        name: "ZKTeco Test Device",
        adapter_type: :zkteco,
        ip_address: "***************",
        port: 4370
      )
    end

    let!(:generic_device) do
      create(:attendance_device,
        name: "Generic HTTP Test Device",
        adapter_type: :generic_http,
        ip_address: "*************"
      )
    end

    let!(:file_device) do
      create(:attendance_device, :file_import,
        name: "File Import Test Device"
      )
    end

    context "ZKTeco adapter" do
      it "creates adapter successfully" do
        adapter = zkteco_device.create_adapter

        expect(adapter).to be_present
        expect(adapter.class.name).to eq("Attendance::Adapters::ZktecoAdapter")
        expect(adapter.device_ip).to eq("***************")
        expect(adapter.device_port).to eq(4370)
      end

      it "supports expected capabilities" do
        adapter = zkteco_device.create_adapter

        expect(adapter.supports_real_time?).to be_in([true, false])
        expect(adapter.supports_user_management?).to be_in([true, false])
        expect(adapter.supports_clear_data?).to be_in([true, false])
      end
    end

    context "Generic HTTP adapter" do
      it "creates adapter successfully" do
        adapter = generic_device.create_adapter

        expect(adapter).to be_present
        expect(adapter.class.name).to eq("Attendance::Adapters::GenericHttpAdapter")
        expect(adapter.adapter_type).to eq("generic_http")
      end
    end

    context "File Import adapter" do
      it "creates adapter successfully" do
        adapter = file_device.create_adapter

        expect(adapter).to be_present
        expect(adapter.class.name).to eq("Attendance::Adapters::FileImportAdapter")
        expect(adapter.adapter_type).to eq("file_import")
      end
    end
  end

  describe "Sync Log Management" do
    let!(:device) { create(:attendance_device, adapter_type: :zkteco) }
    let!(:sync_log) do
      create(:attendance_sync_log,
        attendance_device: device,
        status: :success,
        sync_type: :manual,
        started_at: 1.hour.ago,
        completed_at: 30.minutes.ago
      )
    end

    it "manages sync logs successfully" do
      expect(Attendance::SyncLog.count).to eq(1)
      expect(Attendance::SyncLog.recent.first).to eq(sync_log)
    end

    it "provides sync log information" do
      recent_log = Attendance::SyncLog.recent.first

      expect(recent_log.attendance_device).to eq(device)
      expect(recent_log.status).to eq("success")
      expect(recent_log.sync_type).to eq("manual")
      expect(recent_log.started_at).to be_present
      expect(recent_log.completed_at).to be_present
    end
  end

  describe "Health Monitoring" do
    let!(:healthy_device) do
      create(:attendance_device,
        adapter_type: :zkteco,
        status: :active,
        last_seen_at: 5.minutes.ago
      )
    end

    let!(:unhealthy_device) do
      create(:attendance_device,
        adapter_type: :generic_http,
        status: :error,
        last_seen_at: 2.days.ago
      )
    end

    it "calculates health scores" do
      expect(healthy_device.sync_health_score).to be >= 0
      expect(unhealthy_device.sync_health_score).to be >= 0
    end

    it "tracks device connectivity" do
      expect(healthy_device.last_seen_at).to be_present
      expect(unhealthy_device.last_seen_at).to be_present

      # Recent activity indicates healthy device
      expect(healthy_device.last_seen_at).to be > 1.hour.ago
      # Old activity indicates unhealthy device
      expect(unhealthy_device.last_seen_at).to be < 1.day.ago
    end
  end

  describe "Worker Classes" do
    it "loads all attendance worker classes successfully" do
      workers = [
        'Attendance::DeviceSyncWorker',
        'Attendance::MultiDeviceSyncWorker',
        'Attendance::DeviceHealthMonitorWorker'
      ]

      workers.each do |worker_name|
        expect { worker_name.constantize }.not_to raise_error
        worker_class = worker_name.constantize
        expect(worker_class).to be_present
      end
    end
  end

  describe "Model Associations" do
    let!(:device) { create(:attendance_device) }
    let!(:employee) { create(:employee) }
    let!(:sync_log) { create(:attendance_sync_log, attendance_device: device) }
    let!(:event) do
      create(:attendance_event,
        employee: employee,
        source_device: device,
        timestamp: Time.current.to_i
      )
    end

    it "maintains proper device associations" do
      expect(device.attendance_sync_logs).to include(sync_log)
      expect(device.attendance_events).to include(event)
    end

    it "maintains proper event associations" do
      expect(event.employee).to eq(employee)
      expect(event.source_device).to eq(device)
    end
  end

  describe "API Serializers" do
    it "loads all attendance serializers successfully" do
      serializers = [
        'Attendance::DeviceSerializer',
        'Attendance::SyncLogSerializer'
      ]

      serializers.each do |serializer_name|
        expect { serializer_name.constantize }.not_to raise_error
        serializer_class = serializer_name.constantize
        expect(serializer_class).to be_present
      end
    end
  end

  describe "Database Schema" do
    it "has all required attendance tables" do
      tables = [
        'attendance_devices',
        'attendance_sync_logs',
        'attendance_events'
      ]

      tables.each do |table_name|
        expect(ActiveRecord::Base.connection.table_exists?(table_name)).to be true
      end
    end

    it "can query attendance tables" do
      expect { Attendance::Device.count }.not_to raise_error
      expect { Attendance::SyncLog.count }.not_to raise_error
      expect { Attendance::Event.count }.not_to raise_error
    end
  end

  describe "System Configuration" do
    it "has proper environment configuration" do
      expect(Rails.env).to be_present
      expect(ActiveRecord::Base.connection.adapter_name).to be_present
    end

    it "can connect to Redis for background jobs" do
      redis_available = begin
        Sidekiq.redis { |conn| conn.ping }
        true
      rescue
        false
      end

      # Redis should be available in test environment
      expect(redis_available).to be true
    end
  end

  describe "System Summary" do
    let!(:devices) { create_list(:attendance_device, 3, adapter_type: :zkteco, status: :active) }
    let!(:sync_logs) { create_list(:attendance_sync_log, 5, attendance_device: devices.first) }
    let!(:events) { create_list(:attendance_event, 10, source_device: devices.first) }

    it "provides accurate system statistics" do
      expect(Attendance::Device.count).to eq(3)
      expect(Attendance::Device.active.count).to eq(3)
      expect(Attendance::SyncLog.count).to eq(5)
      expect(Attendance::Event.count).to eq(10)

      device_types = Attendance::Device.distinct.pluck(:adapter_type)
      expect(device_types).to include("zkteco")
    end
  end
end
