# frozen_string_literal: true

# Support module for mocking gRPC calls in tests
module GrpcMocking
  def mock_active_rpc_for_employee!
    # Mock the active_rpc method to prevent gRPC integration setup
    Employee.stubs(:active_rpc).returns(true)

    # Mock individual gRPC methods that might already exist
    Employee.any_instance.stubs(:update_remote_user).returns(true)
    Employee.any_instance.stubs(:validate_remote_user).returns(true)
    Employee.any_instance.stubs(:load_user_data).returns(nil)
    Employee.any_instance.stubs(:user_data).returns(
      OpenStruct.new(
        message: OpenStruct.new(
          name: "Test User",
          email: "<EMAIL>",
          roles: [],
          permissions: []
        )
      )
    )
  end

  def with_grpc_mocked(&block)
    mock_active_rpc_for_employee!
    yield
  end
end

# Include in RSpec
RSpec.configure do |config|
  config.include GrpcMocking
end
