# RSpec Testing Framework

This project uses RSpec for testing. This document provides an overview of the testing setup and conventions.

## Running Tests

To run all tests:

```bash
bundle exec rspec
```

To run a specific test file:

```bash
bundle exec rspec spec/models/employee_spec.rb
```

To run a specific test (by line number):

```bash
bundle exec rspec spec/models/employee_spec.rb:10
```

## Directory Structure

- `spec/models/` - Tests for models
- `spec/controllers/` - Tests for controllers
- `spec/services/` - Tests for service objects
- `spec/workers/` - Tests for background workers
- `spec/factories/` - Factory Bot factories
- `spec/fixtures/` - Test fixtures
- `spec/support/` - Test helpers and shared examples

## Test Helpers

### Authentication

For controller tests, you can use the following helpers:

```ruby
# Mock authentication
mock_authenticate_session!

# Mock authorization
mock_authorize!(true)
```

### API Testing

For API tests, you can use the `json_response` helper:

```ruby
# Parse the JSON response
data = json_response["data"]
```

## Factories

We use Factory Bot for creating test data. Factories are defined in `spec/factories/`.

Example:

```ruby
# Create an employee
employee = create(:employee)

# Create an employee with specific attributes
employee = create(:employee, department: :finance)
```

## Mocking and Stubbing

We use Mocha for mocking and stubbing. Example:

```ruby
# Stub a method
Employee.any_instance.stubs(:user_data).returns(mock_response)

# Set expectations
Employee.any_instance.expects(:reload_user).once
```

## Sidekiq Testing

Sidekiq tests are configured to run in fake mode by default. This means that jobs are not actually enqueued but are stored in memory for testing.

```ruby
# Test that a job was enqueued
expect {
  # Code that enqueues a job
}.to change(PeriodCalculationWorker.jobs, :size).by(1)
```
