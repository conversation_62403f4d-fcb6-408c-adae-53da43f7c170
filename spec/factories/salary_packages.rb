FactoryBot.define do
  factory :salary_package do
    employee
    base_salary { 1000.0 }
    housing_allowance { 200.0 }
    transportation_allowance { 100.0 }
    other_allowances { 50.0 }
    effective_date { Date.current.beginning_of_month }
    end_date { nil }
    notes { 'Standard salary package' }

    trait :with_end_date do
      end_date { Date.current.end_of_month }
    end

    trait :high_salary do
      base_salary { 5000.0 }
      housing_allowance { 1000.0 }
      transportation_allowance { 500.0 }
      other_allowances { 250.0 }
    end

    trait :basic_salary do
      base_salary { 500.0 }
      housing_allowance { 0.0 }
      transportation_allowance { 0.0 }
      other_allowances { 0.0 }
    end
  end
end
