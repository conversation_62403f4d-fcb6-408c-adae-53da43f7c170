FactoryBot.define do
  factory :attendance_summary do
    employee
    date { Date.current }
    first_check_in { Time.current.beginning_of_day + 9.hours }
    last_check_out { Time.current.beginning_of_day + 17.hours }
    total_duration_minutes { 480 } # 8 hours
    work_status { 'complete' }
    event_count { 2 }
    undetermined_count { 0 }
    notes { nil }

    trait :incomplete do
      work_status { 'incomplete' }
      total_duration_minutes { 240 } # 4 hours
      last_check_out { nil }
    end

    trait :with_overtime do
      total_duration_minutes { 600 } # 10 hours
      last_check_out { Time.current.beginning_of_day + 19.hours }
    end

    trait :late_arrival do
      first_check_in { Time.current.beginning_of_day + 10.hours }
      total_duration_minutes { 420 } # 7 hours
    end

    trait :early_departure do
      last_check_out { Time.current.beginning_of_day + 16.hours }
      total_duration_minutes { 420 } # 7 hours
    end
  end
end
