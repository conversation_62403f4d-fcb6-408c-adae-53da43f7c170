FactoryBot.define do
  factory :attendance_sync_log, class: 'Attendance::SyncLog' do
    association :attendance_device, factory: :attendance_device
    association :triggered_by, factory: :employee

    status { :pending }
    sync_type { :manual }
    started_at { Time.current }
    completed_at { nil }

    sync_params do
      {
        start_date: Date.yesterday.to_s,
        end_date: Date.today.to_s
      }
    end

    result_summary { {} }
    error_details { {} }

    trait :running do
      status { :running }
      started_at { 5.minutes.ago }
    end

    trait :completed do
      status { :success }
      started_at { 10.minutes.ago }
      completed_at { 5.minutes.ago }
      result_summary do
        {
          total_processed: 100,
          success_count: 95,
          failure_count: 5,
          new_records: 80,
          updated_records: 15
        }
      end
    end

    trait :failed do
      status { :failed }
      started_at { 10.minutes.ago }
      completed_at { 8.minutes.ago }
      error_details do
        {
          error_class: "StandardError",
          error_message: "Connection timeout",
          error_backtrace: ["line 1", "line 2"]
        }
      end
    end

    trait :partial do
      status { :partial }
      started_at { 15.minutes.ago }
      completed_at { 10.minutes.ago }
      result_summary do
        {
          total_processed: 100,
          success_count: 70,
          failure_count: 30,
          new_records: 60,
          updated_records: 10
        }
      end
    end

    trait :scheduled do
      sync_type { :scheduled }
    end

    trait :real_time do
      sync_type { :real_time }
    end

    trait :retry do
      sync_type { :retry }
    end
  end
end
