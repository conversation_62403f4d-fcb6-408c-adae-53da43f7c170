FactoryBot.define do
  factory :attendance_event, class: 'Attendance::Event' do
    association :employee
    association :source_device, factory: :attendance_device
    timestamp { Time.current.to_i }
    event_type { :check_in }
    activity_type { :regular }
    potential_duplicate { false }
    skip_period_calculation { true } # Skip period calculation in tests by default

    # Use save(validate: false) to bypass validations
    to_create { |instance| instance.save(validate: false) }

    trait :check_in do
      event_type { :check_in }
    end

    trait :check_out do
      event_type { :check_out }
    end

    trait :lunch_break do
      activity_type { :lunch }
    end

    trait :regular_activity do
      activity_type { :regular }
    end

    # Specific time-based factories
    factory :morning_check_in do
      event_type { :check_in }
      activity_type { :regular }
      timestamp { Time.current.beginning_of_day.change(hour: 9, min: 15).to_i }
    end

    factory :lunch_check_out do
      event_type { :check_out }
      activity_type { :lunch }
      timestamp { Time.current.beginning_of_day.change(hour: 12, min: 30).to_i }
    end

    factory :lunch_check_in do
      event_type { :check_in }
      activity_type { :regular }
      timestamp { Time.current.beginning_of_day.change(hour: 13, min: 15).to_i }
    end

    factory :evening_check_out do
      event_type { :check_out }
      activity_type { :regular }
      timestamp { Time.current.beginning_of_day.change(hour: 17, min: 30).to_i }
    end
  end
end
