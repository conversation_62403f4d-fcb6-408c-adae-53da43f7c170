FactoryBot.define do
  # Factory for creating complete attendance scenarios
  factory :attendance_scenario, class: OpenStruct do
    transient do
      employee { nil }
      date { Date.current }
    end

    # Don't create an actual model, just use this for building scenarios
    skip_create

    trait :complete_day do
      after(:build) do |scenario, evaluator|
        base_time = evaluator.date.beginning_of_day

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 9, min: 15).to_i,
               event_type: :check_in,
               activity_type: :regular
        )

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 12, min: 30).to_i,
               event_type: :check_out,
               activity_type: :lunch
        )

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 13, min: 15).to_i,
               event_type: :check_in,
               activity_type: :regular
        )

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 17, min: 30).to_i,
               event_type: :check_out,
               activity_type: :regular
        )
      end
    end

    trait :missing_check_in do
      after(:build) do |scenario, evaluator|
        base_time = evaluator.date.beginning_of_day

        # Start with lunch break (no initial check-in)
        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 12, min: 30).to_i,
               event_type: :check_out,
               activity_type: :lunch
        )

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 13, min: 15).to_i,
               event_type: :check_in,
               activity_type: :regular
        )

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 17, min: 30).to_i,
               event_type: :check_out,
               activity_type: :regular
        )
      end
    end

    trait :missing_check_out do
      after(:build) do |scenario, evaluator|
        base_time = evaluator.date.beginning_of_day

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 9, min: 15).to_i,
               event_type: :check_in,
               activity_type: :regular
        )

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 12, min: 30).to_i,
               event_type: :check_out,
               activity_type: :lunch
        )

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 13, min: 15).to_i,
               event_type: :check_in,
               activity_type: :regular
        )

        # No final check-out
      end
    end

    trait :with_duplicates do
      after(:build) do |scenario, evaluator|
        base_time = evaluator.date.beginning_of_day

        # Create normal events first
        check_in_time = base_time.change(hour: 9, min: 15).to_i

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: check_in_time,
               event_type: :check_in,
               activity_type: :regular
        )

        # Create duplicate 30 seconds later
        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: check_in_time + 30,
               event_type: :check_in,
               activity_type: :regular
        )

        # Continue with normal events
        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 12, min: 30).to_i,
               event_type: :check_out,
               activity_type: :lunch
        )

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 13, min: 15).to_i,
               event_type: :check_in,
               activity_type: :regular
        )

        create(:attendance_event,
               employee: evaluator.employee,
               timestamp: base_time.change(hour: 17, min: 30).to_i,
               event_type: :check_out,
               activity_type: :regular
        )
      end
    end
  end
end
