FactoryBot.define do
  factory :employee do
    # Use transient attributes for mock data that will be stubbed
    transient do
      mock_name { Faker::Name.name }
      mock_email { Faker::Internet.unique.email }
    end

    department { Employee::DEPARTMENT_LABELS.keys.sample.to_s }
    start_date { Date.today - rand(1..365).days }
    phone { '+962790000000' }
    exempt_from_attendance_deductions { false }

    # Mock user_id to avoid RPC calls during tests
    user_id { rand(1..100) }
    status { :active }

    # Use save(validate: false) with proper gRPC mocking
    after(:build) do |instance, evaluator|
      # Mock all the gRPC and user-related methods
      instance.stubs(:update_remote_user).returns(true)
      instance.stubs(:validate_remote_user).returns(true)
      instance.stubs(:load_user_data).returns(nil)
      instance.stubs(:name).returns(evaluator.mock_name)
      instance.stubs(:email).returns(evaluator.mock_email)
      instance.stubs(:user_data).returns(
        OpenStruct.new(
          message: OpenStruct.new(
            name: evaluator.mock_name,
            email: evaluator.mock_email
          )
        )
      )

      # Mock any other user-related callbacks that might be causing issues
      instance.stubs(:sync_user_data).returns(true)
      instance.stubs(:update_user_record).returns(true)
      instance.stubs(:create_user_record).returns(true)
      instance.stubs(:active_rpc).returns(true)
      instance.stubs(:reload_user_data).returns(true)

      # Skip all validations and callbacks related to user management
      instance.define_singleton_method(:run_callbacks) do |*args, &block|
        if args.first.to_s.include?('save') || args.first.to_s.include?('create') || args.first.to_s.include?('update')
          block.call if block
        else
          super(*args, &block)
        end
      end
    end

    # Use save without validation to avoid gRPC issues
    to_create { |instance| instance.save(validate: false) }

    trait :with_user_roles do
      user_roles_list do
        [
          {
            'role' => {
              'id' => '1',
              'name' => 'employee',
              'global' => false
            },
            'project' => {
              'id' => '1',
              'name' => 'Project Alpha'
            },
            'is_default' => true
          }
        ]
      end
    end

    factory :employee_with_user_roles, traits: [:with_user_roles]

    trait :exempt_from_attendance_deductions do
      exempt_from_attendance_deductions { true }
    end

    factory :exempt_employee, traits: [:exempt_from_attendance_deductions]
  end
end
