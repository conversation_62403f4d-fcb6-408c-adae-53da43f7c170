FactoryBot.define do
  factory :leave do
    employee
    leave_type { :annual }
    leave_duration { :full_day }
    status { :pending }
    start_date { Date.current }
    end_date { Date.current + 5.days }
    reason { 'Vacation time' }

    # Skip callbacks for testing
    to_create { |instance| instance.save(validate: false) }

    # Define a trait for half-day leaves
    trait :half_day do
      leave_duration { :half_day_morning }
      start_date { Date.current }
      end_date { Date.current }
    end

    # Define traits for different leave types
    trait :sick do
      leave_type { :sick }
      reason { 'Medical appointment' }
    end

    trait :marriage do
      leave_type { :marriage }
      reason { 'Getting married' }
    end

    trait :maternity do
      leave_type { :maternity }
      reason { 'Maternity leave' }
      start_date { Date.current }
      end_date { Date.current + 70.days }
    end

    trait :paternity do
      leave_type { :paternity }
      reason { 'Paternity leave' }
      start_date { Date.current }
      end_date { Date.current + 3.days }
    end

    trait :unpaid do
      leave_type { :unpaid }
      reason { 'Personal matters' }
    end

    # Define traits for different statuses
    trait :approved do
      status { :approved }
    end

    trait :rejected do
      status { :rejected }
    end

    trait :withdrawn do
      status { :withdrawn }
    end
  end

  # Define a trait for leaves with documents
  trait :with_documents do
    transient do
      document_count { 1 }
    end

    after(:create) do |leave, evaluator|
      # Create test documents
      evaluator.document_count.times do |i|
        leave.documents.attach(
          io: StringIO.new("test document content #{i}"),
          filename: "document_#{i}.pdf",
          content_type: "application/pdf"
        )
      end
    end
  end
end
