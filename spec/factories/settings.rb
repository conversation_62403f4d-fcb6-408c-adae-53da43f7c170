FactoryBot.define do
  factory :setting do
    namespace { 'attendance' }
    key { 'work_start_time' }
    value { '09:00' }

    # Use save(validate: false) to bypass validations
    to_create { |instance| instance.save(validate: false) }

    trait :work_start_time do
      key { 'work_start_time' }
      value { '09:00' }
    end

    trait :work_end_time do
      key { 'work_end_time' }
      value { '17:00' }
    end

    trait :break_duration do
      key { 'break_duration_minutes' }
      value { '60' }
    end

    trait :late_threshold do
      key { 'late_threshold_minutes' }
      value { '15' }
    end

    trait :early_departure_threshold do
      key { 'early_departure_threshold_minutes' }
      value { '30' }
    end

    # Factory for creating all attendance settings at once
    factory :attendance_settings, class: OpenStruct do
      transient do
        work_start { '09:00' }
        work_end { '17:00' }
        break_duration { '60' }
        late_threshold { '15' }
        early_departure_threshold { '30' }
      end

      skip_create

      after(:build) do |setting, evaluator|
        create(:setting, :work_start_time, value: evaluator.work_start)
        create(:setting, :work_end_time, value: evaluator.work_end)
        create(:setting, :break_duration, value: evaluator.break_duration)
        create(:setting, :late_threshold, value: evaluator.late_threshold)
        create(:setting, :early_departure_threshold, value: evaluator.early_departure_threshold)
      end
    end
  end
end
