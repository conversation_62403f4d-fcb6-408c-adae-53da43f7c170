require 'rails_helper'

RSpec.describe Salary::AttendanceIntegration, type: :model do
  let(:employee) { build(:employee, exempt_from_attendance_deductions: false) }
  let(:exempt_employee) { build(:employee, exempt_from_attendance_deductions: true) }
  let(:salary_package) { build(:salary_package, employee: employee, base_salary: 1000) }
  let(:exempt_salary_package) { build(:salary_package, employee: exempt_employee, base_salary: 1000) }

  let(:calculation) do
    SalaryCalculation.new(
      employee: employee,
      salary_package: salary_package,
      period_start_date: Date.current.beginning_of_month,
      period_end_date: Date.current.end_of_month,
      gross_salary: 1000,
      period: Date.current.strftime('%Y-%m')
    )
  end

  let(:exempt_calculation) do
    SalaryCalculation.new(
      employee: exempt_employee,
      salary_package: exempt_salary_package,
      period_start_date: Date.current.beginning_of_month,
      period_end_date: Date.current.end_of_month,
      gross_salary: 1000,
      period: Date.current.strftime('%Y-%m')
    )
  end

  # Create a test class that includes the concern
  let(:test_class) do
    Class.new do
      include Salary::AttendanceIntegration

      attr_accessor :employee

      def initialize(employee)
        @employee = employee
      end

      def calculate_working_days(start_date, end_date)
        (start_date..end_date).count { |date| (1..5).include?(date.wday) }
      end

      def count_approved_leave_days(start_date, end_date)
        0 # Simplified for testing
      end

      def calculate_late_arrivals_deduction(start_date, end_date, gross_salary)
        100 # Simplified for testing
      end

      def calculate_early_departures_deduction(start_date, end_date, gross_salary)
        50 # Simplified for testing
      end
    end
  end

  describe '#calculate_attendance_deductions' do
    context 'when employee is exempt from attendance deductions' do
      it 'returns 0 and creates exemption detail' do
        service = test_class.new(exempt_employee)

        result = service.calculate_attendance_deductions(exempt_calculation)

        expect(result).to eq(0)
        expect(exempt_calculation.calculation_details.size).to eq(1)

        exemption_detail = exempt_calculation.calculation_details.first
        expect(exemption_detail.detail_type).to eq('exemption')
        expect(exemption_detail.category).to eq('attendance_exempt')
        expect(exemption_detail.amount).to eq(0)
        expect(exemption_detail.description).to eq('Employee is exempt from attendance deductions')
      end
    end

    context 'when employee is not exempt from attendance deductions' do
      it 'calculates attendance deductions normally' do
        service = test_class.new(employee)

        result = service.calculate_attendance_deductions(calculation)

        # Should return sum of late arrivals + early departures deductions
        # (missed days would be 0 since we have attendance)
        expect(result).to be > 0
        expect(calculation.calculation_details.size).to be >= 1
      end

      it 'creates calculation details for deductions' do
        service = test_class.new(employee)

        service.calculate_attendance_deductions(calculation)

        # Should have details for late arrivals and early departures
        late_detail = calculation.calculation_details.find { |d| d.category == 'attendance_late' }
        early_detail = calculation.calculation_details.find { |d| d.category == 'attendance_early' }

        expect(late_detail).to be_present
        expect(late_detail.amount).to eq(100)
        expect(early_detail).to be_present
        expect(early_detail.amount).to eq(50)
      end
    end
  end
end
