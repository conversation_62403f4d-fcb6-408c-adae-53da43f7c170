require 'rails_helper'

# Tests for handling binary data in Employee avatar_attributes
# These tests verify that our binary data handling solution works correctly
# by testing both the avatar= method and direct assignment to avatar_attributes
RSpec.describe Employee, type: :model do
  describe "avatar handling" do
    it "can set binary data in avatar_attributes" do
      employee = Employee.new

      # Create a mock file with binary data
      binary_data = File.binread(Rails.root.join('spec', 'fixtures', 'files', 'test_image.png')) rescue "\x00\x01\x02\x03\x04"
      mock_file = OpenStruct.new(
        read: binary_data,
        original_filename: 'test_image.png',
        content_type: 'image/png',
        size: binary_data.bytesize
      )

      # Set the avatar
      employee.avatar = mock_file

      # Set the URL directly on avatar_attributes
      employee.avatar_attributes.url = 'https://example.com/avatar.png'

      # Verify the avatar attributes were set correctly
      expect(employee.avatar_attributes.image).to eq(binary_data)
      expect(employee.avatar_attributes.filename).to eq('test_image.png')
      expect(employee.avatar_attributes.content_type).to eq('image/png')
      expect(employee.avatar_attributes.url).to eq('https://example.com/avatar.png')
      expect(employee.avatar_url).to eq('https://example.com/avatar.png')
      expect(employee.avatar_attributes.bytes_size).to eq(binary_data.bytesize)

      # Verify we can get the original binary data back
      expect(employee.avatar_attributes.image).to eq(binary_data)
      expect(employee.avatar_attributes.filename).to eq('test_image.png')
      expect(employee.avatar_attributes.content_type).to eq('image/png')
      expect(employee.avatar_attributes.bytes_size).to eq(binary_data.bytesize)

      # Verify serialization works
      json = employee.as_json
      expect(json["avatar_attributes"]).not_to be_nil

      # The binary data should be included directly in the serialized output
      serialized = json["avatar_attributes"]
      expect(serialized).to be_a(Hash)
      expect(serialized["image"]).to eq(binary_data)
      expect(serialized["url"]).to eq('https://example.com/avatar.png')
      expect(serialized["bytes_size"]).to eq(binary_data.bytesize)

      # Verify we can save the employee
      expect { employee.save }.not_to raise_error
    end

    it "handles direct binary data assignment" do
      employee = Employee.new

      # Set avatar_attributes directly with binary data
      binary_data = "\x00\x01\x02\x03\x04"
      employee.avatar_attributes = Employees::AvatarStruct.new(
        image: binary_data,
        filename: 'test.bin',
        content_type: 'application/octet-stream',
        url: 'https://example.com/test.bin',
        bytes_size: binary_data.bytesize
      )

      # Verify the data was set correctly
      expect(employee.avatar_attributes.image).to eq(binary_data)

      # Verify we can get the original binary data back
      expect(employee.avatar_attributes.image).to eq(binary_data)
      expect(employee.avatar_attributes.filename).to eq('test.bin')
      expect(employee.avatar_attributes.content_type).to eq('application/octet-stream')
      expect(employee.avatar_attributes.url).to eq('https://example.com/test.bin')
      expect(employee.avatar_url).to eq('https://example.com/test.bin')
      expect(employee.avatar_attributes.bytes_size).to eq(binary_data.bytesize)

      # Verify serialization works
      json = employee.as_json
      expect(json["avatar_attributes"]).not_to be_nil

      # The binary data should be included directly in the serialized output
      serialized = json["avatar_attributes"]
      expect(serialized).to be_a(Hash)
      expect(serialized["image"]).to eq(binary_data)
      expect(serialized["url"]).to eq('https://example.com/test.bin')
      expect(serialized["bytes_size"]).to eq(binary_data.bytesize)

      # Verify we can save the employee
      expect { employee.save }.not_to raise_error
    end
  end
end
