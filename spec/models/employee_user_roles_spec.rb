# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Employee, type: :model do
  describe 'user_roles integration' do
    let(:employee) { create(:employee) }

    before do
      # Mock the RPC client to avoid actual RPC calls
      Employee.any_instance.stubs(:update_user).returns(true)
    end

    it 'can add roles using add_role method' do
      employee.add_role({
                          role: { id: '1', name: 'Admin', global: true },
                          project: { id: '1', name: 'Default Project' },
                          is_default: true
                        })

      expect(employee.user_roles_list.size).to eq(1)
      expect(employee.user_roles_list.first.role.id).to eq('1')
    end

    it 'can directly assign user_roles_list' do
      employee.user_roles_list = [
        { role: { id: '1', name: 'Admin', global: true }, project: { id: '1', name: 'Default Project' }, is_default: true },
        { role: { id: '2', name: 'Project Manager', global: false }, project: { id: '1', name: 'Project X' }, is_default: false }
      ]

      expect(employee.user_roles_list.size).to eq(2)
      expect(employee.user_roles_list.first.role.id).to eq('1')
      expect(employee.user_roles_list.last.role.id).to eq('2')
    end

    it 'provides user_role_records for serialization' do
      employee.user_roles_list = [
        { role: { id: '1', name: 'Admin', global: true }, project: { id: '1', name: 'Default Project' }, is_default: true },
        { role: { id: '2', name: 'Project Manager', global: false }, project: { id: '1', name: 'Project X' }, is_default: false }
      ]

      records = employee.user_role_records

      expect(records.size).to eq(2)
      expect(records.first).to be_a(Employees::UserRole)
      expect(records.first.role.id).to eq('1')
    end

    it 'can filter roles by type' do
      employee.user_roles_list = [
        { role: { id: '1', name: 'Admin', global: true }, project: { id: '1', name: 'Default Project' }, is_default: true },
        { role: { id: '2', name: 'Project Manager', global: false }, project: { id: '1', name: 'Project X' }, is_default: false }
      ]

      expect(employee.user_roles_list.global_roles.size).to eq(1)
      expect(employee.user_roles_list.project_roles.size).to eq(1)
    end

    describe 'collection methods' do
      before do
        employee.user_roles_list = [
          { role: { id: '1', name: 'Admin', global: true }, project: { id: '1', name: 'Default Project' }, is_default: true },
          { role: { id: '2', name: 'Project Manager', global: false }, project: { id: '1', name: 'Project X' }, is_default: false },
          { role: { id: '3', name: 'Developer', global: false }, project: { id: '2', name: 'Project Y' }, is_default: false }
        ]
      end

      it 'correctly implements first and last methods' do
        expect(employee.user_roles_list.first.role.id).to eq('1')
        expect(employee.user_roles_list.last.role.id).to eq('3')
      end

      it 'correctly implements to_a method' do
        array = employee.user_roles_list.to_a
        expect(array).to be_an(Array)
        expect(array.size).to eq(3)
        expect(array.map { |ur| ur.role.id }).to eq(['1', '2', '3'])
      end

      it 'correctly implements each method' do
        role_ids = []
        employee.user_roles_list.each do |role|
          role_ids << role.role.id
        end
        expect(role_ids).to eq(['1', '2', '3'])
      end

      it 'correctly implements enumerable methods' do
        # map
        role_ids = employee.user_roles_list.map { |ur| ur.role.id }
        expect(role_ids).to eq(['1', '2', '3'])

        # select
        global_roles = employee.user_roles_list.select { |ur| ur.role.global }
        expect(global_roles.size).to eq(1)
        expect(global_roles.first.role.id).to eq('1')

        # find
        role = employee.user_roles_list.find { |r| r.role.id == '2' }
        expect(role).not_to be_nil
        expect(role.role.name).to eq('Project Manager')
      end

      it 'correctly implements offset and limit' do
        # offset
        offset_collection = employee.user_roles_list.offset(1)
        expect(offset_collection.to_a.size).to eq(2)
        expect(offset_collection.first.role.id).to eq('2')

        # limit - should return first 2 items
        limit_collection = employee.user_roles_list.limit(2)
        expect(limit_collection.to_a.size).to eq(2)
        # The limit method returns the first N items from the collection
        first_two_ids = employee.user_roles_list.to_a.first(2).map { |ur| ur.role.id }
        expect(limit_collection.map { |ur| ur.role.id }).to eq(first_two_ids)

        # both offset and limit
        paginated = employee.user_roles_list.offset(1).limit(1)
        expect(paginated.to_a.size).to eq(1)
        expect(paginated.first.role.id).to eq('2')
      end
    end
  end
end
