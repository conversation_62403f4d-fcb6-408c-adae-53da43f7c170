require 'rails_helper'

RSpec.describe EmployeeSearchData, type: :model do
  before do
    # Make sure the backing table exists
    unless ActiveRecord::Base.connection.table_exists?('employee_search_views_data')
      ActiveRecord::Base.connection.create_table :employee_search_views_data, id: false do |t|
        t.bigint :employee_id, primary_key: true
        t.integer :department
        t.date :start_date
        t.integer :status
        t.bigint :user_id
        t.string :name
        t.string :email
        t.text :search_document
      end
    end

    # Create the materialized view if it doesn't exist
    unless ActiveRecord::Base.connection.execute("SELECT to_regclass('employee_search_data')").first['to_regclass'].present?
      ActiveRecord::Base.connection.execute(<<-SQL)
        CREATE MATERIALIZED VIEW employee_search_data AS
        SELECT
          esd.employee_id,
          esd.department,
          esd.start_date,
          esd.status,
          esd.user_id,
          esd.name,
          esd.email,
          esd.search_document,
          to_tsvector('english', esd.search_document) AS search_vector
        FROM
          employee_search_views_data esd
      SQL
    end
  end

  describe ".full_text_search" do
    it "finds employees by name" do
      # Create employee using the factory
      employee = create(:employee,
        mock_name: '<PERSON>',
        mock_email: '<EMAIL>',
        user_id: 400,
        department: :hr,
        start_date: Date.today,
        status: :active
      )



      # The factory already mocks the gRPC data

      # Manually trigger search data update since gRPC is mocked
      EmployeeSearchData.update_for_employee(employee)

      # Refresh the materialized view
      EmployeeSearchData.refresh

      # Search for the employee by name
      results = EmployeeSearchData.full_text_search("John")
      expect(results.count).to eq(1)
      expect(results.first.employee_id).to eq(employee.id)

      # Search for the employee by full last name (this works)
      results = EmployeeSearchData.full_text_search("Smith")
      expect(results.count).to eq(1)
      expect(results.first.employee_id).to eq(employee.id)
    end

    it "finds employees by email" do
      # Create employee using the factory
      employee = create(:employee,
        mock_name: 'Jane Doe',
        mock_email: '<EMAIL>',
        user_id: 401,
        department: :finance,
        start_date: Date.today,
        status: :active
      )

      # Manually trigger search data update since gRPC is mocked
      EmployeeSearchData.update_for_employee(employee)

      # Refresh the materialized view
      EmployeeSearchData.refresh

      # Search for the employee by email
      results = EmployeeSearchData.full_text_search("jane.doe")
      expect(results.count).to eq(1)
      expect(results.first.employee_id).to eq(employee.id)

      # Search for the employee by partial email
      results = EmployeeSearchData.full_text_search("example")
      expect(results.count).to eq(1)
      expect(results.first.employee_id).to eq(employee.id)
    end

    it "finds employees by department" do
      # Create employee using the factory
      employee = create(:employee,
        mock_name: 'Bob Johnson',
        mock_email: '<EMAIL>',
        user_id: 402,
        department: :it,
        start_date: Date.today,
        status: :active
      )

      # Manually trigger search data update since gRPC is mocked
      EmployeeSearchData.update_for_employee(employee)

      # Refresh the materialized view
      EmployeeSearchData.refresh

      # Search for the employee by department name
      results = EmployeeSearchData.full_text_search("Technology")
      expect(results.count).to eq(1)
      expect(results.first.employee_id).to eq(employee.id)
    end
  end

  describe "Employee.search_employees" do
    it "finds employees" do
      # Create employee using the factory
      employee = create(:employee,
        mock_name: 'Alice Brown',
        mock_email: '<EMAIL>',
        user_id: 403,
        department: :programs,
        start_date: Date.today,
        status: :active
      )

      # Manually trigger search data update since gRPC is mocked
      EmployeeSearchData.update_for_employee(employee)

      # Refresh the materialized view
      EmployeeSearchData.refresh

      # Search for the employee
      results = Employee.search_employees("Alice")
      expect(results.count).to eq(1)
      expect(results.first.id).to eq(employee.id)
    end
  end

  describe ".sql_safe_value" do
    it "handles different types of values correctly" do
      # Test nil value
      expect(EmployeeSearchData.sql_safe_value(nil)).to eq('NULL')

      # Test empty string
      expect(EmployeeSearchData.sql_safe_value('')).to eq('NULL')
      expect(EmployeeSearchData.sql_safe_value('   ')).to eq('NULL')

      # Test integer
      expect(EmployeeSearchData.sql_safe_value(42)).to eq('42')

      # Test numeric string
      expect(EmployeeSearchData.sql_safe_value('123')).to eq('123')

      # Test non-numeric string (should be NULL by default)
      expect(EmployeeSearchData.sql_safe_value('abc')).to eq('NULL')

      # Test non-numeric string with allow_string=true
      value = EmployeeSearchData.sql_safe_value('abc', true)
      expect(value).to eq("'abc'")

      # Test status enum string values
      expect(EmployeeSearchData.sql_safe_value('active')).to eq('0')
      expect(EmployeeSearchData.sql_safe_value('inactive')).to eq('1')
      expect(EmployeeSearchData.sql_safe_value('pending')).to eq('2')

      # Test other types
      expect(EmployeeSearchData.sql_safe_value(true)).to eq('NULL')
      expect(EmployeeSearchData.sql_safe_value(1.5)).to eq('NULL')
    end
  end
end
