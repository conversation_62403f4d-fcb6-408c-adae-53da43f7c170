require 'rails_helper'

RSpec.describe Employee, type: :model do
  describe "ransacker for department enum" do
    it "works with string values" do
      # Skip this test if there are no employees in the database
      skip if Employee.count == 0

      # Find or create employees with different departments
      hr_employee = Employee.where(department: :hr).first
      finance_employee = Employee.where(department: :finance).first

      # Skip if we don't have both types of employees
      skip unless hr_employee && finance_employee

      # Test filtering with string value for department_eq
      result = Employee.ransack(department_eq: 'hr').result
      expect(result).to include(hr_employee)
      expect(result).not_to include(finance_employee)

      # Test filtering with string values for department_in
      result = Employee.ransack(department_in: [ 'hr', 'finance' ]).result
      expect(result).to include(hr_employee)
      expect(result).to include(finance_employee)
    end
  end

  describe "user attributes" do
    it "are loaded and accessible" do
      # Skip this test if the gRPC service is not available
      skip "gRPC service not available in test environment"

      # This test would require actual gRPC integration
      # In a real test environment, you would:
      # 1. Mock the gRPC client
      # 2. Set up proper test data
      # 3. Test the actual integration
    end
  end

  describe "active_rpc module" do
    it "works correctly" do
      # Skip this test - ActiveRpc functionality is tested in the rpc-gem
      skip "ActiveRpc functionality is tested in the rpc-gem specs"
    end
  end

  describe "phone field validation" do
    it "works correctly" do
      # Skip this test if Phonofy is not available
      skip unless defined?(Phonofy) && Employee.method_defined?(:phone_is_valid?)

      # Create an employee with a valid phone number (Jordan)
      employee = Employee.new(user_id: 400, department: :hr, start_date: Date.today, status: :active, phone: "+962790000000")
      expect(employee).to be_valid

      # Test phone validation
      expect(employee.phone_is_valid?).to be true

      # Test with a different valid phone number (Egypt)
      employee.phone = "+201234567890" # Egypt number
      expect(employee).to be_valid
      expect(employee.phone_is_valid?).to be true

      # Test phone object methods
      employee.phone = "+962790000000"
      phone_obj = employee.phone_object
      expect(phone_obj.country).to eq("JO")
    end
  end

  describe "user roles functionality" do
    it "works with user_roles_list" do
      # Create an employee
      employee = Employee.new(user_id: 500, department: :hr, start_date: Date.today, status: :active)

      # Test that user_roles_list is initialized
      expect(employee.user_roles_list).to be_a(Employees::UserRoleCollection)

      # Test adding roles with valid UserRole attributes
      employee.add_role({
        role: { id: '1', name: 'Admin', global: true },
        project: { id: '1', name: 'Default Project' },
        is_default: true
      })

      expect(employee.user_roles_list.size).to eq(1)

      # Test the added role properties
      added_role = employee.user_roles_list.first
      expect(added_role).to be_a(Employees::UserRole)
      expect(added_role.role.name).to eq('Admin')
      expect(added_role.role.global).to be true
      expect(added_role.is_default).to be true

      # Test user_role_records method
      expect(employee.user_role_records).to be_an(Array)
      expect(employee.user_role_records.size).to eq(1)

      # Test user_roles alias
      expect(employee.user_roles).to eq(employee.user_roles_list)
    end
  end

  describe 'attendance deduction exemption' do
    describe 'validations' do
      it 'validates exempt_from_attendance_deductions inclusion' do
        employee = Employee.new(user_id: 600, department: :hr, start_date: Date.today, status: :active, phone: '+962790000000')
        employee.exempt_from_attendance_deductions = nil
        expect(employee).not_to be_valid
        expect(employee.errors[:exempt_from_attendance_deductions]).to include('is not included in the list')
      end

      it 'allows true for exempt_from_attendance_deductions' do
        employee = Employee.new(user_id: 601, department: :hr, start_date: Date.today, status: :active, phone: '+962790000000')
        employee.exempt_from_attendance_deductions = true
        expect(employee).to be_valid
      end

      it 'allows false for exempt_from_attendance_deductions' do
        employee = Employee.new(user_id: 602, department: :hr, start_date: Date.today, status: :active, phone: '+962790000000')
        employee.exempt_from_attendance_deductions = false
        expect(employee).to be_valid
      end
    end

    describe 'scopes' do
      it 'has exempt_from_attendance_deductions scope' do
        expect(Employee).to respond_to(:exempt_from_attendance_deductions)
        expect(Employee.exempt_from_attendance_deductions).to be_a(ActiveRecord::Relation)
      end

      it 'has not_exempt_from_attendance_deductions scope' do
        expect(Employee).to respond_to(:not_exempt_from_attendance_deductions)
        expect(Employee.not_exempt_from_attendance_deductions).to be_a(ActiveRecord::Relation)
      end
    end

    describe 'default values' do
      it 'defaults exempt_from_attendance_deductions to false' do
        new_employee = Employee.new
        expect(new_employee.exempt_from_attendance_deductions).to be false
      end
    end
  end
end
