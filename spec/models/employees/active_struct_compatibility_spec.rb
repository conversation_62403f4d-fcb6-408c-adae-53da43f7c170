# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'ActiveStruct ActiveRecord Compatibility' do
  describe 'ActiveStruct::Base' do
    it 'provides column_names method' do
      expect(Employees::Role.column_names).to include('id', 'name', 'global', 'level')
    end

    it 'provides ransackable_attributes method' do
      expect(Employees::Role.ransackable_attributes).to include('id', 'name', 'global', 'level')
    end

    it 'provides model_name method' do
      expect(Employees::Role.model_name).to be_a(ActiveModel::Name)
      expect(Employees::Role.model_name.name).to eq('Employees::Role')
      expect(Employees::Role.model_name.singular).to eq('employees_role')
      expect(Employees::Role.model_name.plural).to eq('employees_roles')
    end

    it 'provides persisted? method' do
      role = Employees::Role.new
      expect(role.persisted?).to be false

      role.id = '123'
      expect(role.persisted?).to be true
    end

    it 'provides new_record? method' do
      role = Employees::Role.new
      expect(role.new_record?).to be true

      role.id = '123'
      expect(role.new_record?).to be false
    end
  end

  describe 'ActiveStruct::Collection' do
    let(:collection) do
      collection = Employees::UserRoleCollection.new
      collection << { id: '1', role: { name: 'Admin', global: true } }
      collection << { id: '2', role: { name: 'Manager', global: false }, project: { name: 'Project X' } }
      collection
    end

    it 'provides model method' do
      expect(collection.model).to eq(Employees::UserRole)
    end

    it 'provides ransack method' do
      ransack_result = collection.ransack(role_id_eq: '1')
      expect(ransack_result).to respond_to(:result)
    end

    it 'provides where method' do
      result = collection.where(id: '1')
      expect(result.size).to eq(1)
      expect(result.first.id).to eq('1')
    end

    it 'provides order method' do
      result = collection.order(id: :desc)
      expect(result.first.id).to eq('2')
      expect(result.last.id).to eq('1')
    end

    it 'provides page method' do
      # Check that the collection responds to page and per
      expect(collection).to respond_to(:page)
      expect(collection).to respond_to(:per)

      # Call the methods to make sure they don't raise errors
      expect { collection.page(1) }.not_to raise_error
      expect { collection.per(1) }.not_to raise_error
    end

    it 'provides offset method' do
      result = collection.offset(1)
      expect(result.to_a.size).to eq(1)
      expect(result.to_a.first.id).to eq('2')
    end

    it 'provides limit method' do
      result = collection.limit(1)
      expect(result.to_a.size).to eq(1)
      expect(result.to_a.first.id).to eq('1')
    end

    it 'combines offset and limit' do
      result = collection.offset(1).limit(1)
      expect(result.to_a.size).to eq(1)
      expect(result.to_a.first.id).to eq('2')
    end
  end

  describe 'ActiveStruct::Associations' do
    before do
      module Employees
        class TestParent < Athar::Commons::ActiveStruct::Base
          has_many :test_children, class_name: 'Employees::TestChild'
          has_one :test_child, class_name: 'Employees::TestChild'
          belongs_to :test_grandparent, class_name: 'Employees::TestGrandparent'
        end

        class TestChild < Athar::Commons::ActiveStruct::Base
          attribute :name, :string
          attribute :test_parent_id, :string
        end

        class TestChildCollection
          include Athar::Commons::ActiveStruct::Collection
          collection_item_class TestChild
        end

        class TestGrandparent < Athar::Commons::ActiveStruct::Base
        end
      end
    end

    after do
      Employees.send(:remove_const, :TestParent) if Employees.const_defined?(:TestParent)
      Employees.send(:remove_const, :TestChild) if Employees.const_defined?(:TestChild)
      Employees.send(:remove_const, :TestChildCollection) if Employees.const_defined?(:TestChildCollection)
      Employees.send(:remove_const, :TestGrandparent) if Employees.const_defined?(:TestGrandparent)
    end

    it 'provides reflect_on_all_associations method' do
      # Get all associations
      associations = Employees::TestParent.reflect_on_all_associations

      # Check that we have the expected associations
      has_many_assoc = associations.find { |a| a.macro == :has_many && a.name == :test_children }
      expect(has_many_assoc).to be_present
      expect(has_many_assoc.name).to eq(:test_children)

      has_one_assoc = associations.find { |a| a.macro == :has_one && a.name == :test_child }
      expect(has_one_assoc).to be_present
      expect(has_one_assoc.name).to eq(:test_child)

      belongs_to_assoc = associations.find { |a| a.macro == :belongs_to && a.name == :test_grandparent }
      expect(belongs_to_assoc).to be_present
      expect(belongs_to_assoc.name).to eq(:test_grandparent)
    end

    it 'provides reflect_on_association method' do
      association = Employees::TestParent.reflect_on_association(:test_children)
      expect(association.macro).to eq(:has_many)
      expect(association.name).to eq(:test_children)
    end

    it 'provides _ids methods for has_many associations' do
      parent = Employees::TestParent.new

      # Add some children
      parent.test_children << { id: '1', name: 'Child 1' }
      parent.test_children << { id: '2', name: 'Child 2' }

      expect(parent.test_child_ids).to eq(['1', '2'])

      # Set children by IDs
      parent.test_child_ids = ['3', '4']
      expect(parent.test_children.size).to eq(2)
      expect(parent.test_child_ids).to eq(['3', '4'])
    end

    it 'provides _id methods for has_one associations' do
      parent = Employees::TestParent.new

      # Set a child
      parent.test_child = { id: '1', name: 'Child 1' }

      expect(parent.test_child_id).to eq('1')

      # Set child by ID
      parent.test_child_id = '2'
      expect(parent.test_child.id).to eq('2')
    end
  end
end
