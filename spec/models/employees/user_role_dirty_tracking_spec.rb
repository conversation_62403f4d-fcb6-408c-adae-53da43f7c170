# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Employee UserRole Dirty Tracking', type: :model do
  before do
    # Mock the RPC client to avoid actual RPC calls
    Employee.any_instance.stubs(:update_user).returns(true)
  end

  it 'tracks changes to user_roles_list' do
    # Create an employee with minimal required attributes
    employee = Employee.new(
      name: 'Test Employee',
      email: '<EMAIL>',
      phone: '+962790000000',
      start_date: Date.today
    )

    # Save the employee
    employee.save(validate: false)

    # Reset changes manually
    employee.clear_changes_information

    # No changes initially
    expect(employee.changed?).to be_falsey

    # Add a role
    employee.user_roles_list = [{ role: { name: 'Admin', global: true } }]

    # Check that changes are tracked
    expect(employee.changed?).to be_truthy
    expect(employee.changed).to include('user_roles_list')

    # Save the changes
    employee.save(validate: false)

    # Reset changes manually
    employee.clear_changes_information

    # No changes after reset
    expect(employee.changed?).to be_falsey

    # Add another role
    employee.user_roles_list << { role: { name: 'Editor', global: false } }

    # Check that changes are tracked
    expect(employee.changed?).to be_truthy
    expect(employee.changed).to include('user_roles_list')

    # Save the changes
    employee.save(validate: false)

    # Reset changes manually
    employee.clear_changes_information

    # No changes after reset
    expect(employee.changed?).to be_falsey

    # Clear the roles
    employee.user_roles_list.clear

    # Check that changes are tracked
    expect(employee.changed?).to be_truthy
    expect(employee.changed).to include('user_roles_list')
  end
end
