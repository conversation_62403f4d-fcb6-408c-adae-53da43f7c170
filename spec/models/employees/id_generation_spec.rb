# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'ID Generation in ActiveStruct Models' do
  describe 'auto-generated ID configuration' do
    it 'does not auto-generate IDs for Role' do
      role = Employees::Role.new(name: 'Admin', global: true)
      expect(role.id).to be_nil
    end

    it 'does not auto-generate IDs for Project' do
      project = Employees::Project.new(name: 'Project X', description: 'A test project')
      expect(project.id).to be_nil
    end

    it 'does not auto-generate IDs for UserRole' do
      user_role = Employees::UserRole.new(is_default: true)
      expect(user_role.id).to be_nil
    end

    it 'allows setting IDs manually' do
      role = Employees::Role.new(id: '123', name: 'Admin', global: true)
      expect(role.id).to eq('123')
    end

    it 'auto-generates IDs for other ActiveStruct models' do
      # Create a test class that inherits from ActiveStruct::Base
      test_class = Class.new(Athar::Commons::ActiveStruct::Base) do
        attribute :name, :string
      end

      # Create an instance without an ID
      instance = test_class.new(name: 'Test')

      # Verify that an ID was auto-generated
      expect(instance.id).not_to be_nil
      expect(instance.id).to be_a(String)
      expect(instance.id.length).to be > 0
    end
  end
end
