# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'ActiveStruct Dirty Tracking' do
  describe 'ActiveStruct::Base dirty tracking' do
    it 'tracks changes to attributes' do
      # Create a test class that inherits from ActiveStruct::Base
      test_class = Class.new(Athar::Commons::ActiveStruct::Base) do
        attribute :name, :string
        attribute :description, :string
      end

      # Create an instance
      instance = test_class.new(name: 'Test')

      # No changes after initialization
      expect(instance.changed?).to be_falsey

      # Change an attribute
      instance.name = 'Updated'

      # Check that changes are tracked
      expect(instance.changed?).to be_truthy
      expect(instance.changed).to include('name')
      expect(instance.changes).to include('name' => ['Test', 'Updated'])

      # Change another attribute
      instance.description = 'New description'

      # Check that both changes are tracked
      expect(instance.changed).to include('name', 'description')
      expect(instance.changes).to include(
        'name' => ['Test', 'Updated'],
        'description' => [nil, 'New description']
      )
    end
  end

  describe 'UserRoleCollection dirty tracking' do
    it 'tracks changes to the collection' do
      # Create a collection
      collection = Employees::UserRoleCollection.new

      # Add a role
      collection << { role: { name: 'Admin', global: true } }

      # Check that changes are tracked
      expect(collection.changed?).to be_truthy

      # Reset changed flag
      collection.changed = false

      # Change the collection again
      collection.clear

      # Check that changes are tracked
      expect(collection.changed?).to be_truthy
    end

    it 'tracks changes in an ActiveStruct type' do
      # Create a test class that uses UserRoleCollection
      test_class = Class.new(Athar::Commons::ActiveStruct::Base) do
        attribute :name, :string
        attribute :roles, :user_role_collection
      end

      # Create an instance
      instance = test_class.new(name: 'Test')

      # No changes after initialization
      expect(instance.changed?).to be_falsey

      # Set roles
      instance.roles = [{ role: { name: 'Admin', global: true } }]

      # Check that changes are tracked
      expect(instance.changed?).to be_truthy
      expect(instance.changed).to include('roles')

      # Reset changes
      instance.clear_changes_information

      # Modify the collection
      instance.roles << { role: { name: 'Editor', global: false } }

      # Check that changes are tracked
      expect(instance.changed?).to be_truthy
      expect(instance.changed).to include('roles')
    end

    it 'tracks changes in the Employee model' do
      # Create a mock Employee class for testing
      test_employee_class = Class.new do
        include ActiveModel::Model
        include ActiveModel::Attributes
        include ActiveModel::Dirty

        attribute :user_roles_list, :user_role_collection

        def save(options = {})
          changes_applied
          true
        end
      end

      # Create an instance
      employee = test_employee_class.new

      # No changes initially
      expect(employee.changed?).to be_falsey

      # Initialize with empty collection
      employee.user_roles_list = []

      # Check that changes are tracked
      expect(employee.changed?).to be_truthy
      expect(employee.changed).to include('user_roles_list')

      # Save the changes
      employee.save

      # No changes after save
      expect(employee.changed?).to be_falsey

      # Add a role
      employee.user_roles_list << { role: { name: 'Admin', global: true } }

      # Check that changes are tracked
      expect(employee.changed?).to be_truthy
      expect(employee.changed).to include('user_roles_list')

      # Save the changes
      employee.save

      # No changes after save
      expect(employee.changed?).to be_falsey

      # Change the collection again
      employee.user_roles_list.clear

      # Check that changes are tracked
      expect(employee.changed?).to be_truthy
      expect(employee.changed).to include('user_roles_list')
    end
  end
end
