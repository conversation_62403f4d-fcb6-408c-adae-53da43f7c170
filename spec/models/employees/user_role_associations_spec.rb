# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Employees::UserRole do
  describe 'associations' do
    it 'can be associated with a project using an object' do
      project = Employees::Project.new(name: 'Test Project', description: 'A test project')
      user_role = Employees::UserRole.new(project: project)

      # Since Project has disable_id_generation, project.id will be nil
      expect(user_role.project_id).to be_nil
      expect(user_role.project).to eq(project)
      expect(user_role.project.name).to eq('Test Project')
    end

    it 'can be associated with a role using an object' do
      role = Employees::Role.new(name: 'Developer', global: false)
      user_role = Employees::UserRole.new(role: role)

      # Since Role has disable_id_generation, role.id will be nil
      expect(user_role.role_id).to be_nil
      expect(user_role.role).to eq(role)
      expect(user_role.role.name).to eq('Developer')
    end

    it 'can be associated with a project using a hash' do
      user_role = Employees::UserRole.new(project: { name: 'Hash Project', description: 'Created from hash' })

      # The project should be created from the hash, but ID won't be auto-generated
      expect(user_role.project).to be_a(Employees::Project)
      expect(user_role.project.name).to eq('Hash Project')
      expect(user_role.project.description).to eq('Created from hash')
      # Note: project_id will be nil since Project has disable_id_generation
      expect(user_role.project_id).to be_nil
    end

    it 'can be associated with a role using a hash' do
      user_role = Employees::UserRole.new(role: { name: 'Hash Role', global: true })

      # The role should be created from the hash, but ID won't be auto-generated
      expect(user_role.role).to be_a(Employees::Role)
      expect(user_role.role.name).to eq('Hash Role')
      expect(user_role.role.global).to eq(true)
      # Note: role_id will be nil since Role has disable_id_generation
      expect(user_role.role_id).to be_nil
    end

    it 'serializes associated objects in as_json' do
      user_role = Employees::UserRole.new(
        project: { name: 'Serialized Project', description: 'For serialization test' },
        role: { name: 'Serialized Role', global: true },
        is_default: true
      )

      json = user_role.as_json

      expect(json['project']).to be_present
      expect(json['project']['name']).to eq('Serialized Project')
      expect(json['project']['description']).to eq('For serialization test')

      expect(json['role']).to be_present
      expect(json['role']['name']).to eq('Serialized Role')
      expect(json['role']['global']).to eq(true)

      expect(json['is_default']).to eq(true)
    end
  end
end
