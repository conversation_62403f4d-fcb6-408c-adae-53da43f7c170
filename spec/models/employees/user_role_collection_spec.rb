# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Employees::UserRoleCollection do
  let(:admin_role) do
    Employees::UserRole.new(
      role: { name: 'Admin', global: true },
      is_default: true
    )
  end

  let(:project_role) do
    Employees::UserRole.new(
      role: { name: 'Project Manager', global: false },
      project: { name: 'Project X', id: '1' }
    )
  end

  describe 'initialization' do
    it 'can be initialized with an array of roles' do
      collection = Employees::UserRoleCollection.new(nil, nil, [
        { role: { name: 'Admin', global: true } },
        { role: { name: 'Project Manager', global: false }, project: { name: 'Project X', id: '1' } }
      ])

      expect(collection.size).to eq(2)
      expect(collection.first.role.name).to eq('Admin')
      expect(collection.last.role.name).to eq('Project Manager')
    end

    it 'can be initialized with owner information' do
      collection = Employees::UserRoleCollection.new(1, :employee)

      expect(collection.owner_id).to eq(1)
      expect(collection.owner_type).to eq(:employee)
    end
  end

  describe '#<<' do
    it 'adds a role to the collection' do
      collection = Employees::UserRoleCollection.new
      collection << admin_role

      expect(collection.size).to eq(1)
      expect(collection.first).to eq(admin_role)
    end

    it 'converts a hash to a role' do
      collection = Employees::UserRoleCollection.new
      collection << { role: { name: 'Admin', global: true } }

      expect(collection.size).to eq(1)
      expect(collection.first.role.name).to eq('Admin')
      expect(collection.first.global?).to eq(true)
    end

    it 'handles an array input' do
      collection = Employees::UserRoleCollection.new
      collection << [ { role: { name: 'Admin', global: true } } ]

      expect(collection.size).to eq(1)
      expect(collection.first.role.name).to eq('Admin')
      expect(collection.first.global?).to eq(true)
    end
  end

  describe 'filtering methods' do
    let(:collection) do
      collection = Employees::UserRoleCollection.new
      collection << admin_role
      collection << project_role
      collection
    end

    describe '#global_roles' do
      it 'returns only global roles' do
        global_roles = collection.global_roles

        expect(global_roles.size).to eq(1)
        expect(global_roles.first.role.name).to eq('Admin')
      end
    end

    describe '#project_roles' do
      it 'returns only project roles' do
        project_roles = collection.project_roles

        expect(project_roles.size).to eq(1)
        expect(project_roles.first.role.name).to eq('Project Manager')
      end
    end

    describe '#default_role' do
      it 'returns the default role' do
        default_role = collection.default_role

        expect(default_role).to eq(admin_role)
      end
    end

    describe '#find_by_role_id' do
      it 'finds a role by its ID' do
        role = collection.find_by_role_id(admin_role.role.id)

        expect(role).to eq(admin_role)
      end
    end

    describe '#find_by_project_id' do
      it 'finds roles by project ID' do
        roles = collection.find_by_project_id('1')

        expect(roles.size).to eq(1)
        expect(roles.first).to eq(project_role)
      end
    end
  end

  describe '#as_json' do
    it 'returns an array of role hashes' do
      collection = Employees::UserRoleCollection.new
      collection << admin_role
      collection << project_role

      json = collection.as_json

      expect(json.size).to eq(2)
      expect(json.first['role']['name']).to eq('Admin')
      expect(json.last['role']['name']).to eq('Project Manager')
    end
  end
end
