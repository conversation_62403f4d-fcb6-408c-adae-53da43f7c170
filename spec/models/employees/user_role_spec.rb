# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Employees::UserRole do
  describe 'initialization' do
    it 'can be initialized with associations' do
      role = Employees::Role.new(name: 'Admin', global: true)
      project = Employees::Project.new(name: 'Project X', description: 'A test project')

      user_role = Employees::UserRole.new(
        role: role,
        project: project,
        is_default: true
      )

      expect(user_role.role).to eq(role)
      expect(user_role.project).to eq(project)
      expect(user_role.is_default).to eq(true)
    end

    it 'can be initialized with hash attributes for associations' do
      user_role = Employees::UserRole.new(
        role: { name: 'Admin', global: true },
        project: { name: 'Project X', description: 'A test project' },
        is_default: true
      )

      expect(user_role.role.name).to eq('Admin')
      expect(user_role.role.global).to eq(true)
      expect(user_role.project.name).to eq('Project X')
      expect(user_role.project.description).to eq('A test project')
      expect(user_role.is_default).to eq(true)
    end

    it 'can be initialized with an array containing a hash' do
      user_role = Employees::UserRole.new([
                                            {
                                              role: { name: 'Admin', global: true },
                                              project: { name: 'Project X', description: 'A test project' },
                                              is_default: true
                                            }
                                          ])

      expect(user_role.role.name).to eq('Admin')
      expect(user_role.role.global).to eq(true)
      expect(user_role.project.name).to eq('Project X')
      expect(user_role.is_default).to eq(true)
    end
  end

  describe '#as_json' do
    it 'returns a hash with associations' do
      user_role = Employees::UserRole.new(
        role: { name: 'Admin', global: true },
        project: { name: 'Project X', description: 'A test project' },
        is_default: true
      )

      json = user_role.as_json

      expect(json['role']).to be_present
      expect(json['role']['name']).to eq('Admin')
      expect(json['role']['global']).to eq(true)

      expect(json['project']).to be_present
      expect(json['project']['name']).to eq('Project X')
      expect(json['project']['description']).to eq('A test project')

      expect(json['is_default']).to eq(true)
    end
  end

  describe '#global?' do
    it 'returns true when role is global' do
      user_role = Employees::UserRole.new(
        role: { name: 'Admin', global: true }
      )

      expect(user_role.global?).to be true
    end

    it 'returns false when role is not global' do
      user_role = Employees::UserRole.new(
        role: { name: 'Project Manager', global: false }
      )

      expect(user_role.global?).to be false
    end
  end
end
