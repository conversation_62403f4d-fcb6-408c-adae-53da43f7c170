require 'rails_helper'

RSpec.describe Attendance::BatchPeriodCalculationWorker, type: :worker do
  include ActiveSupport::Testing::TimeHelpers
  let(:employee1) { create(:employee) }
  let(:employee2) { create(:employee) }
  let(:start_date) { Date.new(2023, 1, 1) }
  let(:end_date) { Date.new(2023, 1, 3) }

  before do
    # Use Sidekiq test mode
    Sidekiq::Testing.fake!

    # Mock Employee gRPC methods to prevent hanging
    Employee.any_instance.stubs(:update_remote_user).returns(true)
    Employee.any_instance.stubs(:validate_remote_user).returns(true)
    Employee.any_instance.stubs(:load_user_data).returns(nil)
    Employee.any_instance.stubs(:sync_user_data).returns(true)
    Employee.any_instance.stubs(:update_user_record).returns(true)
    Employee.any_instance.stubs(:create_user_record).returns(true)
    Employee.any_instance.stubs(:active_rpc).returns(true)
    Employee.any_instance.stubs(:reload_user_data).returns(true)
  end

  after do
    # Clear jobs after each test
    Sidekiq::Queues.clear_all
  end

  describe "#perform" do
    it "queues individual jobs for each employee and date" do
      # Stub the logger to prevent actual logging during tests
      Rails.logger.stubs(:info)
      Rails.logger.stubs(:error)

      # Mock Employee.all to return our test employees
      Employee.stubs(:all).returns([employee1, employee2])

      # Execute the worker
      worker = Attendance::BatchPeriodCalculationWorker.new
      worker.perform(start_date.to_s, end_date.to_s)

      # Check that the correct number of jobs were queued
      # 2 employees × 3 days = 6 jobs
      expect(Sidekiq::Queues["attendance"].size).to eq(6)

      # Verify each job has the correct parameters
      expected_jobs = []
      [employee1, employee2].each do |employee|
        (start_date..end_date).each do |date|
          expected_jobs << [employee.id, date.to_s]
        end
      end

      actual_jobs = Sidekiq::Queues["attendance"].map { |job| job["args"] }
      expected_jobs.each do |expected_args|
        expect(actual_jobs).to include(expected_args)
      end
    end

    it "handles string date parameters correctly" do
      # Stub the logger
      Rails.logger.stubs(:info)
      Rails.logger.stubs(:error)

      # Mock Employee.all to return our test employees
      Employee.stubs(:all).returns([employee1, employee2])

      # Execute the worker with string dates
      worker = Attendance::BatchPeriodCalculationWorker.new
      worker.perform("2023-02-01", "2023-02-02")

      # Check jobs were queued with correct dates
      date_strings = Sidekiq::Queues["attendance"].map { |job| job["args"][1] }.uniq.sort
      expect(date_strings).to eq(["2023-02-01", "2023-02-02"])
    end

    it "defaults to yesterday when no dates provided" do
      # Stub the logger
      Rails.logger.stubs(:info)
      Rails.logger.stubs(:error)

      # Mock Employee.all to return our test employees
      Employee.stubs(:all).returns([employee1, employee2])

      # Freeze time to ensure consistent "yesterday"
      travel_to Time.zone.local(2023, 3, 15) do
        yesterday = Date.new(2023, 3, 14).to_s

        # Execute the worker with no dates
        worker = Attendance::BatchPeriodCalculationWorker.new
        worker.perform

        # Check all jobs use yesterday's date
        date_strings = Sidekiq::Queues["attendance"].map { |job| job["args"][1] }.uniq
        expect(date_strings).to eq([yesterday])
      end
    end

    it "handles specific employee ID parameter" do
      # Stub the logger
      Rails.logger.stubs(:info)
      Rails.logger.stubs(:error)

      # Execute the worker for a specific employee
      worker = Attendance::BatchPeriodCalculationWorker.new
      worker.perform(start_date.to_s, end_date.to_s, employee1.id)

      # Check only jobs for the specified employee were queued
      employee_ids = Sidekiq::Queues["attendance"].map { |job| job["args"][0] }.uniq
      expect(employee_ids).to eq([employee1.id])

      # Check the correct number of jobs (1 employee × 3 days = 3 jobs)
      expect(Sidekiq::Queues["attendance"].size).to eq(3)
    end

    it "handles employee not found gracefully" do
      # Stub the logger except for the error we want to verify
      Rails.logger.stubs(:info)
      Rails.logger.expects(:error).with(includes("Failed to find employees")).once

      # Execute the worker with a non-existent employee ID
      worker = Attendance::BatchPeriodCalculationWorker.new
      worker.perform(start_date.to_s, end_date.to_s, 999999)

      # Check that jobs were still queued with the fallback mechanism
      expect(Sidekiq::Queues["attendance"].size).to eq(3)
    end

    it "logs error when date parsing fails" do
      # Expect error logging - allow any error calls since there might be multiple
      Rails.logger.stubs(:info)
      Rails.logger.stubs(:error)

      # Execute the worker with invalid date
      worker = Attendance::BatchPeriodCalculationWorker.new

      # This should raise an error that gets caught and logged
      expect {
        worker.perform("not-a-date", end_date.to_s)
      }.to raise_error(Date::Error)
    end
  end

  describe "private methods" do
    it "work correctly" do
      worker = Attendance::BatchPeriodCalculationWorker.new

      # Test parse_dates
      start_date, end_date = worker.send(:parse_dates, "2023-04-01", "2023-04-03")
      expect(start_date).to eq(Date.new(2023, 4, 1))
      expect(end_date).to eq(Date.new(2023, 4, 3))

      # Test find_employees with specific ID
      Employee.expects(:find).with(employee1.id).returns(employee1)
      employees = worker.send(:find_employees, employee1.id)
      expect(employees).to eq([employee1])

      # Test find_employees with no ID
      Employee.expects(:all).returns([employee1, employee2])
      employees = worker.send(:find_employees, nil)
      expect(employees).to eq([employee1, employee2])
    end
  end
end
