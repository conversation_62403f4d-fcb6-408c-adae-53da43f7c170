require 'rails_helper'

RSpec.describe Statistics::Calculators::RemainingLeaveBalanceCalculator do
  let(:calculator) { described_class.new }
  let(:employee) { mock('Employee') }
  let(:context) { { employee_id: 1 } }

  before do
    Employee.stubs(:find).with(1).returns(employee)
  end

  describe '#validate_context' do
    it 'raises an error when employee_id is missing' do
      expect { calculator.calculate({}) }.to raise_error(ArgumentError, /Missing required context: employee_id/)
    end

    it 'sets default year to current year when not provided' do
      calculator.validate_context(context)
      expect(context[:year]).to eq(Date.current.year)
      expect(context[:start_date]).to eq(Date.new(Date.current.year, 1, 1))
      expect(context[:end_date]).to eq(Date.new(Date.current.year, 12, 31))
    end

    it 'uses provided year correctly' do
      context[:year] = 2024
      calculator.validate_context(context)
      expect(context[:year]).to eq(2024)
      expect(context[:start_date]).to eq(Date.new(2024, 1, 1))
      expect(context[:end_date]).to eq(Date.new(2024, 12, 31))
    end
  end

  describe '#perform_calculation' do
    let(:mock_leaves) { mock('ActiveRecord::Relation') }
    let(:start_date) { Date.new(2024, 1, 1) }
    let(:end_date) { Date.new(2024, 12, 31) }
    let(:prev_start_date) { Date.new(2023, 1, 1) }
    let(:prev_end_date) { Date.new(2023, 12, 31) }

    before do
      context[:employee] = employee
      context[:year] = 2024
      context[:start_date] = start_date
      context[:end_date] = end_date
      context[:comparison_period] = :year
      context[:comparison_text] = 'compared to last year'

      # Mock current year annual leaves (used 5.5 days)
      current_relation = mock('CurrentRelation')
      current_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", start_date, end_date).returns(current_relation)
      current_relation.stubs(:where).with(status: :approved).returns(current_relation)
      current_relation.stubs(:where).with(leave_type: :annual).returns(current_relation)
      current_relation.stubs(:sum).returns(5.5)

      # Mock previous year annual leaves (used 8.0 days)
      prev_relation = mock('PrevRelation')
      prev_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", prev_start_date, prev_end_date).returns(prev_relation)
      prev_relation.stubs(:where).with(status: :approved).returns(prev_relation)
      prev_relation.stubs(:where).with(leave_type: :annual).returns(prev_relation)
      prev_relation.stubs(:sum).returns(8.0)

      Leave.stubs(:where).with(employee: employee).returns(current_relation, prev_relation)
    end

    it 'returns a metric card with remaining leave balance' do
      card = calculator.perform_calculation(context)

      expect(card.id).to eq('remaining_leave_balance')
      expect(card.title).to eq('Remaining Leave Balance')
      expect(card.value).to eq('15.5') # 21 - 5.5 = 15.5
      expect(card.unit).to eq('days')
      expect(card.comparison_text).to eq('compared to last year')
    end

    it 'calculates percentage change correctly' do
      card = calculator.perform_calculation(context)
      current_balance = 15.5 # 21 - 5.5
      previous_balance = 13.0 # 21 - 8.0
      expected_percentage = ((current_balance - previous_balance) / previous_balance * 100).round(1)
      expect(card.comparison_percentage).to eq(expected_percentage)
      expect(card.trend).to eq('up')
    end

    it 'ensures balance does not go below zero' do
      # Mock scenario where employee used more than entitlement
      current_relation = mock('CurrentRelationOveruse')
      current_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", start_date, end_date).returns(current_relation)
      current_relation.stubs(:where).with(status: :approved).returns(current_relation)
      current_relation.stubs(:where).with(leave_type: :annual).returns(current_relation)
      current_relation.stubs(:sum).returns(25.0) # Used 25 days (more than 21 entitlement)

      prev_relation = mock('PrevRelationOveruse')
      prev_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", prev_start_date, prev_end_date).returns(prev_relation)
      prev_relation.stubs(:where).with(status: :approved).returns(prev_relation)
      prev_relation.stubs(:where).with(leave_type: :annual).returns(prev_relation)
      prev_relation.stubs(:sum).returns(8.0)

      Leave.stubs(:where).with(employee: employee).returns(current_relation, prev_relation)

      card = calculator.perform_calculation(context)
      expect(card.value).to eq('0') # Should not go below 0
    end
  end

  describe '#card_id' do
    it 'returns the correct card id' do
      expect(calculator.card_id).to eq('remaining_leave_balance')
    end
  end

  describe 'ANNUAL_LEAVE_ENTITLEMENT' do
    it 'is set to 21 days' do
      expect(described_class::ANNUAL_LEAVE_ENTITLEMENT).to eq(21)
    end
  end
end
