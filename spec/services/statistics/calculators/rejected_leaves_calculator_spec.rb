require 'rails_helper'

RSpec.describe Statistics::Calculators::RejectedLeavesCalculator do
  let(:calculator) { described_class.new }
  let(:employee) { mock('Employee') }
  let(:context) { { employee_id: 1 } }

  before do
    Employee.stubs(:find).with(1).returns(employee)
  end

  describe '#validate_context' do
    it 'raises an error when employee_id is missing' do
      expect { calculator.calculate({}) }.to raise_error(ArgumentError, /Missing required context: employee_id/)
    end

    it 'sets default dates to current year when not provided' do
      calculator.validate_context(context)
      expect(context[:start_date]).to eq(Date.current.beginning_of_year)
      expect(context[:end_date]).to eq(Date.current.end_of_year)
    end

    it 'parses provided dates correctly' do
      context[:start_date] = '2024-01-01'
      context[:end_date] = '2024-12-31'
      calculator.validate_context(context)
      expect(context[:start_date]).to eq(Date.new(2024, 1, 1))
      expect(context[:end_date]).to eq(Date.new(2024, 12, 31))
    end

    it 'raises an error when end_date is before start_date' do
      context[:start_date] = '2024-12-31'
      context[:end_date] = '2024-01-01'
      expect { calculator.validate_context(context) }.to raise_error(ArgumentError, /End date cannot be before start date/)
    end
  end

  describe '#perform_calculation' do
    let(:mock_leaves) { mock('ActiveRecord::Relation') }
    let(:start_date) { Date.current.beginning_of_year }
    let(:end_date) { Date.current.end_of_year }
    let(:prev_start_date) { start_date.prev_year }
    let(:prev_end_date) { end_date.prev_year }

    before do
      context[:employee] = employee
      context[:start_date] = start_date
      context[:end_date] = end_date
      context[:comparison_period] = :year
      context[:comparison_text] = 'compared to last year'

      # Mock current year rejected leaves
      current_relation = mock('CurrentRelation')
      current_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", start_date, end_date).returns(current_relation)
      current_relation.stubs(:where).with(status: :rejected).returns(current_relation)
      current_relation.stubs(:sum).returns(2.0)

      # Mock previous year rejected leaves
      prev_relation = mock('PrevRelation')
      prev_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", prev_start_date, prev_end_date).returns(prev_relation)
      prev_relation.stubs(:where).with(status: :rejected).returns(prev_relation)
      prev_relation.stubs(:sum).returns(1.0)

      Leave.stubs(:where).with(employee: employee).returns(current_relation, prev_relation)
    end

    it 'returns a metric card with rejected leaves count' do
      card = calculator.perform_calculation(context)

      expect(card.id).to eq('rejected_leaves')
      expect(card.title).to eq('Rejected Leaves')
      expect(card.value).to eq('2.0')
      expect(card.unit).to eq('days')
      expect(card.comparison_text).to eq('compared to last year')
    end

    it 'calculates percentage change correctly' do
      card = calculator.perform_calculation(context)
      expected_percentage = ((2.0 - 1.0) / 1.0 * 100).round(1)
      expect(card.comparison_percentage).to eq(expected_percentage)
      expect(card.trend).to eq('up')
    end

    it 'handles zero previous value correctly' do
      # Mock scenario with no previous rejected leaves
      current_relation = mock('CurrentRelationZero')
      current_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", start_date, end_date).returns(current_relation)
      current_relation.stubs(:where).with(status: :rejected).returns(current_relation)
      current_relation.stubs(:sum).returns(2.0)

      prev_relation = mock('PrevRelationZero')
      prev_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", prev_start_date, prev_end_date).returns(prev_relation)
      prev_relation.stubs(:where).with(status: :rejected).returns(prev_relation)
      prev_relation.stubs(:sum).returns(0)

      Leave.stubs(:where).with(employee: employee).returns(current_relation, prev_relation)

      card = calculator.perform_calculation(context)
      expect(card.comparison_percentage).to eq(0)
      expect(card.trend).to eq('neutral')
    end

    it 'handles zero current value correctly' do
      # Mock scenario with no current rejected leaves
      current_relation = mock('CurrentRelationZeroCurrent')
      current_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", start_date, end_date).returns(current_relation)
      current_relation.stubs(:where).with(status: :rejected).returns(current_relation)
      current_relation.stubs(:sum).returns(0)

      prev_relation = mock('PrevRelationZeroCurrent')
      prev_relation.stubs(:where).with("start_date >= ? AND end_date <= ?", prev_start_date, prev_end_date).returns(prev_relation)
      prev_relation.stubs(:where).with(status: :rejected).returns(prev_relation)
      prev_relation.stubs(:sum).returns(1.0)

      Leave.stubs(:where).with(employee: employee).returns(current_relation, prev_relation)

      card = calculator.perform_calculation(context)
      expect(card.value).to eq('0')
      expect(card.trend).to eq('down')
    end
  end

  describe '#card_id' do
    it 'returns the correct card id' do
      expect(calculator.card_id).to eq('rejected_leaves')
    end
  end
end
