require 'rails_helper'

RSpec.describe Statistics::Calculators::AverageDailyWorkHoursCalculator do
  let(:calculator) { described_class.new }
  let(:employee) { mock('Employee') }
  let(:attendance_periods) { mock('AttendancePeriodRelation') }
  let(:all_attendance_periods) { mock('AttendancePeriodRelation') }

  before do
    # Mock Employee.find
    Employee.stubs(:find).with('1').returns(employee)

    # Mock attendance_periods for employee
    employee.stubs(:attendance_periods).returns(attendance_periods)
    attendance_periods.stubs(:where).with(date: anything).returns(attendance_periods)
    attendance_periods.stubs(:where).with(period_type: anything).returns(attendance_periods)
    attendance_periods.stubs(:sum).with(:duration_minutes).returns(480) # 8 hours in minutes

    # Mock Attendance::Period for all employees
    Attendance::Period.stubs(:where).with(date: anything).returns(all_attendance_periods)
    all_attendance_periods.stubs(:where).with(period_type: anything).returns(all_attendance_periods)
    all_attendance_periods.stubs(:sum).with(:duration_minutes).returns(2400) # 40 hours in minutes
  end

  describe '#validate_context' do
    it 'validates context with employee_id' do
      context = { employee_id: '1' }
      calculator.validate_context(context)

      expect(context[:employee]).to eq(employee)
      expect(context[:start_date]).to be_a(Date)
      expect(context[:end_date]).to be_a(Date)
      expect(context[:comparison_period]).to eq(:previous)
    end

    it 'validates context without employee_id' do
      context = {}
      calculator.validate_context(context)

      expect(context[:employee]).to be_nil
      expect(context[:start_date]).to be_a(Date)
      expect(context[:end_date]).to be_a(Date)
      expect(context[:comparison_period]).to eq(:previous)
    end
  end

  describe '#perform_calculation' do
    it 'calculates average daily work hours for a specific employee' do
      # Mock 5 workdays in the period
      Array.any_instance.stubs(:count).returns(5)

      context = {
        employee_id: '1',
        employee: employee,
        start_date: Date.today.beginning_of_month,
        end_date: Date.today,
        comparison_period: :previous
      }

      card = calculator.calculate(context)

      expect(card.id).to eq('average_daily_work_hours')
      expect(card.title).to eq('Average Daily Work Hours')
      expect(card.value).to eq('1.6') # 8 hours / 5 days = 1.6 hours per day
      expect(card.unit).to eq('hours')
    end

    it 'calculates average daily work hours for all employees' do
      # Mock 5 workdays in the period
      Array.any_instance.stubs(:count).returns(5)

      context = {
        start_date: Date.today.beginning_of_month,
        end_date: Date.today,
        comparison_period: :previous
      }

      card = calculator.calculate(context)

      expect(card.id).to eq('average_daily_work_hours')
      expect(card.title).to eq('Average Daily Work Hours')
      expect(card.value).to eq('8.0') # 40 hours / 5 days = 8 hours per day
      expect(card.unit).to eq('hours')
    end
  end
end
