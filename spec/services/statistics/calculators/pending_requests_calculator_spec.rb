require 'rails_helper'

RSpec.describe Statistics::Calculators::PendingRequestsCalculator do
  let(:calculator) { described_class.new }

  describe '#perform_calculation' do
    before do
      # Mock Leave and ApprovalRequest models
      Leave.stubs(:where).with(status: :pending).returns(stub(count: 10))
      ApprovalRequest.stubs(:where).with(status: :pending).returns(stub(count: 5))
    end

    it 'returns a metric card with the total number of pending requests' do
      card = calculator.calculate({})

      expect(card.id).to eq('pending_requests')
      expect(card.title).to eq('Pending Requests')
      expect(card.value).to eq('15')
      expect(card.unit).to eq('requests')
      expect(card.comparison_percentage).to eq(0) # No comparison
      expect(card.trend).to eq('neutral')
      expect(card.comparison_text).to eq('awaiting approval')
    end
  end
end
