require 'rails_helper'

RSpec.describe Statistics::Calculators::TotalEmployeesCalculator do
  let(:calculator) { described_class.new }
  let(:today) { Date.new(2023, 5, 15) }
  let(:last_month) { Date.new(2023, 4, 30) }

  before do
    # Mock Date.today
    Date.stubs(:today).returns(today)

    # Mock Employee.count for current count
    Employee.stubs(:count).returns(250)

    # Mock Employee.where for previous month
    previous_employees = mock('EmployeeRelation')
    Employee.stubs(:where).with('created_at <= ?', last_month).returns(previous_employees)
    previous_employees.stubs(:count).returns(240)
  end

  describe '#perform_calculation' do
    it 'returns a metric card with the total number of employees' do
      card = calculator.calculate({})

      expect(card.id).to eq('total_employees')
      expect(card.title).to eq('Total Number of Employees')
      expect(card.value).to eq('250')
      expect(card.unit).to eq('employees')
      expect(card.comparison_percentage).to eq(4.2) # (250 - 240) / 240 * 100 = 4.2%
      expect(card.trend).to eq('up')
      expect(card.comparison_text).to eq('compared to last month')
    end
  end
end
