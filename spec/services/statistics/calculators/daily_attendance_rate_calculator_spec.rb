require 'rails_helper'

RSpec.describe Statistics::Calculators::DailyAttendanceRateCalculator do
  let(:calculator) { described_class.new }
  let(:today) { Date.new(2023, 5, 15) } # A Monday
  let(:friday) { Date.new(2023, 5, 12) } # Previous workday (Friday)

  before do
    # Mock Date.today
    Date.stubs(:today).returns(today)

    # Mock Attendance::Period.where for today
    today_periods = mock('AttendancePeriodRelation')
    Attendance::Period.stubs(:where).with(date: today).returns(today_periods)
    today_periods.stubs(:select).with(:employee_id).returns(today_periods)
    today_periods.stubs(:distinct).returns(today_periods)
    today_periods.stubs(:count).returns(213)

    # Mock Attendance::Period.where for Friday
    friday_periods = mock('AttendancePeriodRelation')
    Attendance::Period.stubs(:where).with(date: friday).returns(friday_periods)
    friday_periods.stubs(:select).with(:employee_id).returns(friday_periods)
    friday_periods.stubs(:distinct).returns(friday_periods)
    friday_periods.stubs(:count).returns(205)
  end

  describe '#validate_context' do
    it 'uses today as default date' do
      context = {}
      calculator.validate_context(context)

      expect(context[:date]).to eq(today)
    end

    it 'parses provided date' do
      context = { date: '2023-05-15' }
      calculator.validate_context(context)

      expect(context[:date]).to eq(Date.new(2023, 5, 15))
    end
  end

  describe '#perform_calculation' do
    it 'returns a metric card with the daily attendance rate' do
      context = { date: today }

      card = calculator.calculate(context)

      expect(card.id).to eq('daily_attendance_rate')
      expect(card.title).to eq('Daily Attendance Rate')
      expect(card.value).to eq('213')
      expect(card.unit).to eq('employees')
      expect(card.comparison_percentage).to eq(3.9) # (213 - 205) / 205 * 100 = 3.9%
      expect(card.trend).to eq('up')
      expect(card.comparison_text).to eq('compared to previous workday')
    end
  end
end
