require 'rails_helper'

RSpec.describe Statistics::Helpers::DateHelpers do
  # Create a test class that includes the module
  let(:test_class) do
    Class.new do
      include Statistics::Helpers::DateHelpers
    end
  end

  # Create an instance of the test class
  let(:helper) { test_class.new }

  describe '#parse_date_with_formats' do
    it 'returns nil for nil input' do
      expect(helper.parse_date_with_formats(nil)).to be_nil
    end

    it 'returns the input if it is already a Date' do
      date = Date.today
      expect(helper.parse_date_with_formats(date)).to eq(date)
    end

    it 'parses dates in DD-MM-YYYY format' do
      expect(helper.parse_date_with_formats('01-01-2025')).to eq(Date.new(2025, 1, 1))
    end

    it 'parses dates in YYYY-MM-DD format' do
      expect(helper.parse_date_with_formats('2025-01-01')).to eq(Date.new(2025, 1, 1))
    end

    it 'parses dates in MM/DD/YYYY format' do
      expect(helper.parse_date_with_formats('01/01/2025')).to eq(Date.new(2025, 1, 1))
    end

    it 'parses dates in DD/MM/YYYY format' do
      expect(helper.parse_date_with_formats('01/01/2025')).to eq(Date.new(2025, 1, 1))
    end

    it 'returns nil for invalid date strings' do
      expect(helper.parse_date_with_formats('not-a-date')).to be_nil
    end
  end

  describe '#calculate_previous_period' do
    let(:start_date) { Date.new(2025, 1, 1) }
    let(:end_date) { Date.new(2025, 1, 31) }

    it 'calculates previous month correctly' do
      prev_start_date, prev_end_date = helper.calculate_previous_period(start_date, end_date, :month)

      expect(prev_start_date).to eq(Date.new(2024, 12, 1))
      expect(prev_end_date).to eq(Date.new(2024, 12, 31))
    end

    it 'calculates previous year correctly' do
      prev_start_date, prev_end_date = helper.calculate_previous_period(start_date, end_date, :year)

      expect(prev_start_date).to eq(Date.new(2024, 1, 1))
      expect(prev_end_date).to eq(Date.new(2024, 1, 31))
    end

    it 'calculates previous period of same length correctly' do
      prev_start_date, prev_end_date = helper.calculate_previous_period(start_date, end_date, :previous)

      # 31 days before
      expect(prev_start_date).to eq(Date.new(2024, 12, 1))
      expect(prev_end_date).to eq(Date.new(2024, 12, 31))
    end
  end

  describe '#generate_comparison_text' do
    it 'generates text for month comparison' do
      expect(helper.generate_comparison_text(:month)).to eq('compared to last month')
    end

    it 'generates text for year comparison' do
      expect(helper.generate_comparison_text(:year)).to eq('compared to last year')
    end

    it 'generates text for previous period comparison' do
      expect(helper.generate_comparison_text(:previous)).to eq('compared to previous period')
    end

    it 'handles string inputs' do
      expect(helper.generate_comparison_text('month')).to eq('compared to last month')
    end
  end
end
