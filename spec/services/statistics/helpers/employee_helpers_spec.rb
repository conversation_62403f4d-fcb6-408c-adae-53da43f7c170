require 'rails_helper'

RSpec.describe Statistics::Helpers::EmployeeHelpers do
  # Create a test class that includes the module
  let(:test_class) do
    Class.new do
      include Statistics::Helpers::EmployeeHelpers
    end
  end

  # Create an instance of the test class
  let(:helper) { test_class.new }

  describe '#find_employee' do
    let(:employee) { mock('Employee') }

    before do
      Employee.stubs(:find).with(1).returns(employee)
      Employee.stubs(:find).with(999).raises(ActiveRecord::RecordNotFound)
    end

    it 'returns the employee when found' do
      expect(helper.find_employee(1)).to eq(employee)
    end

    it 'raises an ArgumentError when employee is not found' do
      expect { helper.find_employee(999) }.to raise_error(ArgumentError, "Employee not found with ID: 999")
    end
  end
end
