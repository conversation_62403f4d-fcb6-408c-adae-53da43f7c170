require 'rails_helper'

RSpec.describe Statistics::Helpers::CardHelpers do
  # Create a test class that includes the module
  let(:test_class) do
    Class.new do
      include Statistics::Helpers::CardHelpers
    end
  end

  # Create an instance of the test class
  let(:helper) { test_class.new }

  describe '#calculate_percentage_change' do
    it 'calculates positive percentage change correctly' do
      expect(helper.calculate_percentage_change(110, 100)).to eq(10.0)
    end

    it 'calculates negative percentage change correctly' do
      expect(helper.calculate_percentage_change(90, 100)).to eq(-10.0)
    end

    it 'returns 0 when previous value is 0' do
      expect(helper.calculate_percentage_change(10, 0)).to eq(0)
    end

    it 'rounds to 1 decimal place' do
      expect(helper.calculate_percentage_change(123, 100)).to eq(23.0)
    end
  end

  describe '#determine_trend' do
    it 'returns "up" when current is greater than previous' do
      expect(helper.determine_trend(110, 100)).to eq('up')
    end

    it 'returns "down" when current is less than previous' do
      expect(helper.determine_trend(90, 100)).to eq('down')
    end

    it 'returns "neutral" when current equals previous' do
      expect(helper.determine_trend(100, 100)).to eq('neutral')
    end

    it 'returns "neutral" when previous is zero' do
      expect(helper.determine_trend(10, 0)).to eq('neutral')
    end
  end

  describe '#create_metric_card' do
    before do
      # Mock the MetricCard class
      class Statistics::MetricCard
        attr_reader :id, :title, :value, :unit, :comparison_percentage, :trend, :comparison_text

        def initialize(attributes)
          @id = attributes[:id]
          @title = attributes[:title]
          @value = attributes[:value]
          @unit = attributes[:unit]
          @comparison_percentage = attributes[:comparison_percentage]
          @trend = attributes[:trend]
          @comparison_text = attributes[:comparison_text]
        end
      end
    end

    it 'creates a metric card with the correct attributes' do
      card = helper.create_metric_card(
        'test_card',
        'Test Card',
        10,
        'units',
        10,
        5,
        'compared to test period'
      )

      expect(card.id).to eq('test_card')
      expect(card.title).to eq('Test Card')
      expect(card.value).to eq('10')
      expect(card.unit).to eq('units')
      expect(card.comparison_percentage).to eq(100.0)
      expect(card.trend).to eq('up')
      expect(card.comparison_text).to eq('compared to test period')
    end

    it 'uses default comparison text if not provided' do
      card = helper.create_metric_card(
        'test_card',
        'Test Card',
        10,
        'units',
        10,
        5
      )

      expect(card.comparison_text).to eq('compared to previous period')
    end
  end
end
