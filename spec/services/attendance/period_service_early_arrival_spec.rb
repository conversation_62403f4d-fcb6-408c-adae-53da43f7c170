require 'rails_helper'

RSpec.describe Attendance::PeriodService, type: :service do
  let(:employee_id) { 8 }
  let(:date) { Date.new(2025, 5, 6) }
  
  describe "#calculate_late_and_early_periods" do
    it "creates an early_arrival period when employee arrives before work hours" do
      # Create a mock employee
      employee = mock('Employee')
      employee.stubs(:id).returns(employee_id)
      
      # Create a mock service
      service = Attendance::PeriodService.new(employee, date)
      
      # Mock the settings
      service.stubs(:settings).returns({
        work_start_timestamp: date.to_time.change(hour: 9).to_i,
        work_end_timestamp: date.to_time.change(hour: 17).to_i
      })
      
      # Create mock events with early arrival
      early_arrival_time = date.to_time.change(hour: 8).to_i
      events = [
        mock_event(early_arrival_time, :check_in)
      ]
      
      # Expect the create_period method to be called with early_arrival type
      service.expects(:create_period).with(
        'early_arrival',
        early_arrival_time,
        service.settings[:work_start_timestamp],
        nil,
        false,
        "Early arrival"
      )
      
      # Call the method being tested
      service.send(:calculate_late_and_early_periods, events)
    end
  end
  
  describe "#calculate_work_and_break_periods" do
    it "handles early arrival correctly without creating duplicate periods" do
      # Create a mock employee
      employee = mock('Employee')
      employee.stubs(:id).returns(employee_id)
      
      # Create a mock service
      service = Attendance::PeriodService.new(employee, date)
      
      # Mock the settings
      service.stubs(:settings).returns({
        work_start_timestamp: date.to_time.change(hour: 9).to_i,
        work_end_timestamp: date.to_time.change(hour: 17).to_i
      })
      
      # Create mock events with early arrival
      early_arrival_time = date.to_time.change(hour: 8).to_i
      work_end_time = date.to_time.change(hour: 10).to_i
      events = [
        mock_event(early_arrival_time, :check_in),
        mock_event(work_end_time, :check_out)
      ]
      
      # Expect the create_period method to be called only for the portion during work hours
      service.expects(:create_period).with(
        'work',
        service.settings[:work_start_timestamp],
        work_end_time,
        anything,
        anything,
        nil
      )
      
      # Call the method being tested
      service.send(:calculate_work_and_break_periods, events)
    end
    
    it "creates normal work periods for check-ins during work hours" do
      # Create a mock employee
      employee = mock('Employee')
      employee.stubs(:id).returns(employee_id)
      
      # Create a mock service
      service = Attendance::PeriodService.new(employee, date)
      
      # Mock the settings
      service.stubs(:settings).returns({
        work_start_timestamp: date.to_time.change(hour: 9).to_i,
        work_end_timestamp: date.to_time.change(hour: 17).to_i
      })
      
      # Create mock events with normal work hours
      work_start_time = date.to_time.change(hour: 10).to_i
      work_end_time = date.to_time.change(hour: 12).to_i
      events = [
        mock_event(work_start_time, :check_in),
        mock_event(work_end_time, :check_out)
      ]
      
      # Expect the create_period method to be called for the full period
      service.expects(:create_period).with(
        'work',
        work_start_time,
        work_end_time,
        anything,
        anything,
        nil
      )
      
      # Call the method being tested
      service.send(:calculate_work_and_break_periods, events)
    end
  end
  
  private
  
  def mock_event(timestamp, event_type)
    event = mock('AttendanceEvent')
    event.stubs(:timestamp).returns(timestamp)
    event.stubs(:check_in?).returns(event_type == :check_in)
    event.stubs(:check_out?).returns(event_type == :check_out)
    event.stubs(:activity_type).returns(:regular)
    event.stubs(:id).returns(rand(1000))
    event
  end
end
