require 'rails_helper'

RSpec.describe Attendance::PeriodService, type: :service do
  describe "with incomplete_day flag" do
    let(:employee) { mock('Employee') }
    let(:date) { Date.new(2025, 5, 6) }

    before do
      employee.stubs(:id).returns(1)
    end

    it "does not create early_departure periods when incomplete_day is true" do
      # Create a service with incomplete_day=true
      service = Attendance::PeriodService.new(employee, date, true)

      # Stub the settings
      service.stubs(:settings).returns({
                                         work_start_timestamp: date.to_time.change(hour: 9).to_i,
                                         work_end_timestamp: date.to_time.change(hour: 17).to_i
                                       })

      # Stub the create_period method to track calls
      service.stubs(:create_period)

      # Create a check-in event
      check_in_event = mock('AttendanceEvent')
      check_in_event.stubs(:check_in?).returns(true)
      check_in_event.stubs(:check_out?).returns(false)
      check_in_event.stubs(:timestamp).returns(date.to_time.change(hour: 9).to_i)
      check_in_event.stubs(:id).returns(1)

      # Expect that create_period is never called with early_departure
      service.expects(:create_period).with(
        'early_departure',
        anything,
        anything,
        anything,
        anything,
        anything
      ).never

      # Call the method
      service.send(:calculate_late_and_early_periods, [ check_in_event ])
    end

    it "creates early_departure periods when incomplete_day is false" do
      # Create a service with incomplete_day=false
      service = Attendance::PeriodService.new(employee, date, false)

      # Stub the settings
      service.stubs(:settings).returns({
                                         work_start_timestamp: date.to_time.change(hour: 9).to_i,
                                         work_end_timestamp: date.to_time.change(hour: 17).to_i
                                       })

      # Create a check-in event
      check_in_event = mock('AttendanceEvent')
      check_in_event.stubs(:check_in?).returns(true)
      check_in_event.stubs(:check_out?).returns(false)
      check_in_event.stubs(:timestamp).returns(date.to_time.change(hour: 9).to_i)

      # Create a check-out event that's before work end time
      check_out_event = mock('AttendanceEvent')
      check_out_event.stubs(:check_in?).returns(false)
      check_out_event.stubs(:check_out?).returns(true)
      check_out_event.stubs(:timestamp).returns(date.to_time.change(hour: 15).to_i)

      # Create an array of events
      events = [ check_in_event, check_out_event ]

      # Expect that create_period is called with early_departure
      service.expects(:create_period).with(
        'early_departure',
        check_out_event.timestamp,
        service.settings[:work_end_timestamp],
        nil,
        false,
        "Early departure"
      ).once

      # Call the method
      service.send(:calculate_late_and_early_periods, events)
    end
  end
end
