# frozen_string_literal: true

Rails.application.config.to_prepare do
  require Rails.root.join('app/models/types/avatar_attributes_type')
  require Rails.root.join('app/models/employees/types/user_role_collection_type')
  require Rails.root.join('app/models/employees/types/project_type')
  require Rails.root.join('app/models/employees/types/role_type')
  require Rails.root.join('app/models/statistics/types/metric_card_type')

  # Register the types
  ActiveModel::Type.register(:avatar_attributes_type, Types::AvatarAttributesType)
  ActiveModel::Type.register(:user_role_collection, Employees::Types::UserRoleCollectionType)
  ActiveModel::Type.register(:project, Employees::Types::ProjectType)
  ActiveModel::Type.register(:role, Employees::Types::RoleType)
  ActiveModel::Type.register(:metric_card, Statistics::Types::MetricCardType)

  ActiveRecord::Type.register(:avatar_attributes_type, Types::AvatarAttributesType)
  ActiveRecord::Type.register(:user_role_collection, Employees::Types::UserRoleCollectionType)
  ActiveRecord::Type.register(:project, Employees::Types::ProjectType)
  ActiveRecord::Type.register(:role, Employees::Types::RoleType)
  ActiveRecord::Type.register(:metric_card, Statistics::Types::MetricCardType)
end
