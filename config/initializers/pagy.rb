# frozen_string_literal: true

# Pagy configuration
# See https://ddnexus.github.io/pagy/docs/extras/

# Load the pagy extras needed for JSON:API pagination
require "pagy/extras/metadata"
require "pagy/extras/limit"
require "pagy/extras/jsonapi"

# Set default items per page
Pagy::DEFAULT[:items] = 20
# Set maximum items per page
Pagy::DEFAULT[:max_items] = 100

# Configure JSON:API pagination
# Use page[number] for page number
Pagy::DEFAULT[:page_param] = :number

# Note: The items_param setting doesn't work with the JSONAPI format for nested params
# We manually extract page[size] in the Paginatable concern instead
Pagy::DEFAULT[:items_param] = :size
Pagy::DEFAULT[:limit_param] = :size

# Configure the metadata fields to include in the response
Pagy::DEFAULT[:metadata] = %i[count page limit from to]
