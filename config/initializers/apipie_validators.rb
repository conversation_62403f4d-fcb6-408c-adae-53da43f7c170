# Custom validators for Apipie

# Boolean validator
class BooleanValidator < Apipie::Validator::BaseValidator
  def initialize(param_description, argument)
    super(param_description)
    @type = argument
  end

  def validate(value)
    return true if value.nil? && !param_description.required
    return true if [true, false].include?(value)
    return true if value.is_a?(String) && %w[true false t f yes no y n 1 0].include?(value.downcase)
    false
  end

  def self.build(param_description, argument, options, block)
    if argument == :boolean || argument.to_s == 'Boolean' || argument == TrueClass || argument == FalseClass
      self.new(param_description, argument)
    end
  end

  def description
    "Must be a boolean value."
  end

  def expected_type
    "boolean"
  end
end

# Array validator with better support for comma-separated strings and hash-to-array conversion
class EnhancedArrayValidator < Apipie::Validator::BaseValidator
  def initialize(param_description, argument, options = {}, block = nil)
    super(param_description)
    @array = argument
    @type = options[:of]
    @items_enum = options[:in]
  end

  def validate(value)
    return true if value.nil? && !param_description.required

    # Accept hash with numeric keys (form data format)
    return true if value.is_a?(Hash) && value.keys.all? { |k| k.to_s =~ /\A\d+\z/ }

    return true if value.is_a?(Array)
    return true if value.is_a?(String) && value.include?(",")
    false
  end

  def self.build(param_description, argument, options, block)
    if argument.is_a?(Array) || argument == Array
      self.new(param_description, argument, options, block)
    end
  end

  def description
    "Must be an array or a comma-separated string. Can also be a hash with numeric keys that will be converted to an array."
  end

  def expected_type
    "array"
  end
end
