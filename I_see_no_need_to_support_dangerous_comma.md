
# Simplified Device Commands System Design

Based on the feedback to remove the dangerous_command? functionality and make all commands synchronous, here's a revised system design:

## 1. Command Architecture Overview

### Core Components:
1. **Command Registry**: A central registry of all supported commands
2. **Command Executor**: Service to execute commands on devices synchronously
3. **API Endpoints**: New endpoints to list and execute commands
4. **Command Results**: Standardized response format

## 2. Data Model Changes

### New Models:

#### `Attendance::DeviceCommand` (Registry)
```ruby
class Attendance::DeviceCommand < ApplicationRecord
  # Attributes:
  # - name: string (e.g., "restart", "test_voice")
  # - display_name: string (e.g., "Restart Device", "Test Voice")
  # - description: text
  # - adapter_types: jsonb (which adapter types support this command)
  # - parameters: jsonb (parameter definitions)
  # - enabled: boolean
  
  # Validations, scopes, etc.
end
```

#### `Attendance::CommandExecution` (Audit Log)
```ruby
class Attendance::CommandExecution < ApplicationRecord
  # Attributes:
  # - device_id: references Attendance::Device
  # - command_name: string
  # - parameters: jsonb
  # - status: string (enum: "completed", "failed")
  # - result: jsonb
  # - executed_by_id: references User
  # - executed_at: datetime
  
  # Associations:
  belongs_to :device, class_name: 'Attendance::Device'
  belongs_to :executed_by, class_name: 'User', optional: true
  
  # Scopes, validations, etc.
end
```

## 3. Adapter Enhancement

Extend the `BaseAdapter` class with a command execution interface:

```ruby
# In app/services/attendance/adapters/base_adapter.rb
def execute_command(command_name, parameters = {})
  method_name = "execute_#{command_name}"
  if respond_to?(method_name, true)
    send(method_name, parameters)
  else
    raise NotImplementedError, "Command '#{command_name}' not implemented for #{self.class.name}"
  end
end

# Default implementations for common commands
def execute_restart(params = {})
  raise NotImplementedError, "Restart not implemented"
end

def execute_test_voice(params = {})
  raise NotImplementedError, "Test voice not implemented"
end
```

### ZKTeco Adapter Implementation:

```ruby
# In app/services/attendance/adapters/zkteco_adapter.rb
def execute_restart(params = {})
  with_connection do |zk_client|
    result = zk_client.restart
    { success: true, message: "Device restarting" }
  end
rescue => e
  log_error("Error restarting device", e)
  { success: false, error: e.message }
end

def execute_test_voice(params = {})
  with_connection do |zk_client|
    result = zk_client.test_voice
    { success: true, message: "Voice test initiated" }
  end
rescue => e
  log_error("Error testing voice", e)
  { success: false, error: e.message }
end

def execute_clear_logs(params = {})
  with_connection do |zk_client|
    result = zk_client.clear_logs
    { success: true, message: "Logs cleared successfully" }
  end
rescue => e
  log_error("Error clearing logs", e)
  { success: false, error: e.message }
end

# Implement other commands from the rbzk CLI
```

## 4. Command Execution Service (Simplified)

```ruby
# app/services/attendance/command_executor.rb
module Attendance
  class CommandExecutor
    def self.execute(device, command_name, parameters = {}, user = nil)
      # Validate command exists
      unless available_commands.include?(command_name.to_s)
        return { success: false, error: "Unknown command: #{command_name}" }
      end
      
      # Create execution record
      execution = CommandExecution.create!(
        device: device,
        command_name: command_name,
        parameters: parameters,
        status: :completed,
        executed_by: user,
        executed_at: Time.current
      )
      
      begin
        # Execute command on adapter
        adapter = device.create_adapter
        result = adapter.execute_command(command_name, parameters)
        
        # Update execution record
        execution.update!(
          status: result[:success] ? :completed : :failed,
          result: result
        )
        
        result
      rescue => e
        execution.update!(
          status: :failed,
          result: { success: false, error: e.message }
        )
        
        { success: false, error: e.message }
      end
    end
    
    def self.available_commands
      # This could be loaded from the database in the future
      %w[
        restart test_voice clear_logs clear_lcd write_lcd
        unlock door_state poweroff refresh
      ]
    end
  end
end
```

## 5. API Controller Endpoints

Add new endpoints to the `DevicesController`:

```ruby
# In app/controllers/api/attendance/devices_controller.rb

# List available commands
def available_commands
  authorize!(:execute_commands, :attendance_device)
  
  commands = Attendance::CommandExecutor.available_commands.map do |cmd|
    {
      name: cmd,
      display_name: cmd.titleize
    }
  end
  
  render json: { commands: commands }
end

# Execute a command
def execute_command
  authorize!(:execute_commands, :attendance_device)
  
  command_name = params.require(:command)
  command_params = params.fetch(:parameters, {}).permit!.to_h
  
  result = Attendance::CommandExecutor.execute(
    @attendance_device, 
    command_name, 
    command_params, 
    current_user
  )
  
  if result[:success]
    render json: result, status: :ok
  else
    render json: result, status: :unprocessable_entity
  end
end

# Get command execution history
def command_history
  authorize!(:read, :attendance_device)
  
  executions = @attendance_device.command_executions.order(created_at: :desc).limit(20)
  
  render json: {
    device_id: @attendance_device.id,
    device_name: @attendance_device.name,
    executions: executions.map do |exec|
      {
        id: exec.id,
        command: exec.command_name,
        status: exec.status,
        executed_at: exec.executed_at,
        result: exec.result
      }
    end
  }
end
```

## 6. Routes Configuration

```ruby
# In config/routes.rb
namespace :api do
  namespace :attendance do
    resources :devices do
      member do
        get :available_commands
        post :execute_command
        get :command_history
      end
    end
  end
end
```

## 7. Authorization Updates

Add a new permission for executing commands:

```ruby
# In app/models/ability.rb or wherever permissions are defined
can :execute_commands, :attendance_device if user.has_permission?(:attendance_device_commands)
```

## 8. Frontend Integration

The frontend would need:
1. A command panel in the device detail view
2. A dropdown to select available commands
3. Parameter input fields based on command requirements
4. Confirmation dialogs for potentially destructive commands
5. Status indicators for command execution
6. Command history display

## 9. Implementation Plan

1. **Phase 1**: Core infrastructure
   - Create models for commands and executions
   - Implement adapter command methods
   - Add API endpoints

2. **Phase 2**: Command implementation
   - Implement all commands from the rbzk CLI
   - Add proper error handling and validation

3. **Phase 3**: Frontend integration
   - Build UI components for command execution
   - Add command history display

This simplified design removes the complexity of asynchronous execution and the dangerous command classification, making all commands execute synchronously while still maintaining proper separation of concerns and ensuring security through authorization checks.