module Attendance
  class PeriodService
    attr_reader :employee, :date, :settings, :incomplete_day

    def initialize(employee, date, incomplete_day = false)
      @employee = employee
      @date = date
      @settings = load_settings
      @incomplete_day = incomplete_day
    end

    def calculate_periods
      begin
        # 1. Get all events for the day
        events = ::Attendance::Event.daily_events(employee, date).order(timestamp: :asc)
        Rails.logger.info("Found #{events.size} events for employee #{employee.id} on #{date}")

        # 2. Identify and flag any new potential duplicates
        Attendance::DuplicateDetectionService.identify_duplicates(events, settings[:duplicate_threshold_seconds])

        # 3. Refresh the list of valid events (non-duplicates)
        events = events.where(potential_duplicate: false).order(timestamp: :asc)
        Rails.logger.info("After duplicate detection: #{events.size} valid events")

        # 4. Delete existing periods for this employee and date
        existing_periods = ::Attendance::Period.where(employee: employee, date: date)
        period_count = existing_periods.count
        existing_periods.destroy_all
        Rails.logger.info("Deleted #{period_count} existing periods")

        # 5. Handle missing events (first check-in, last check-out)
        events = handle_missing_events(events)
        Rails.logger.info("After handling missing events: #{events.size} events")

        # 6. Calculate periods
        calculate_work_and_break_periods(events)

        # 7. Calculate late and early departure periods
        calculate_late_and_early_periods(events)

        # Return all periods for this employee and date
        periods = ::Attendance::Period.where(employee: employee, date: date).order(start_timestamp: :asc)
        Rails.logger.info("Created #{periods.size} periods")
        periods
      rescue => e
        # Log the error
        Rails.logger.error("Error calculating periods: #{e.class.name}: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        raise e
      end
    end

    private

    def load_settings
      # Get settings with error handling
      begin
        work_start_time = Setting.attendance_work_start_time
        work_end_time = Setting.attendance_work_end_time
        work_start_timestamp = Setting.attendance_work_start_timestamp(date)
        work_end_timestamp = Setting.attendance_work_end_timestamp(date)
        duplicate_threshold_seconds = Setting.attendance_duplicate_threshold_seconds
        required_work_minutes = Setting.attendance_required_work_minutes
        break_threshold_minutes = Setting.attendance_break_threshold_minutes

        # Log the settings for debugging
        Rails.logger.info("Loaded settings for attendance periods: " +
                         "work_start_time=#{work_start_time}, " +
                         "work_end_time=#{work_end_time}, " +
                         "work_start_timestamp=#{work_start_timestamp}, " +
                         "work_end_timestamp=#{work_end_timestamp}, " +
                         "duplicate_threshold_seconds=#{duplicate_threshold_seconds}, " +
                         "required_work_minutes=#{required_work_minutes}, " +
                         "break_threshold_minutes=#{break_threshold_minutes}")

        # Return the settings hash
        {
          work_start_time: work_start_time,
          work_end_time: work_end_time,
          work_start_timestamp: work_start_timestamp,
          work_end_timestamp: work_end_timestamp,
          duplicate_threshold_seconds: duplicate_threshold_seconds,
          required_work_minutes: required_work_minutes,
          break_threshold_minutes: break_threshold_minutes
        }
      rescue => e
        # Log the error
        Rails.logger.error("Error loading settings: #{e.class.name}: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))

        # Return default settings
        {
          work_start_time: '09:00',
          work_end_time: '17:00',
          work_start_timestamp: date.to_time.change(hour: 9).to_i,
          work_end_timestamp: date.to_time.change(hour: 17).to_i,
          duplicate_threshold_seconds: 60,
          required_work_minutes: 480,
          break_threshold_minutes: 120
        }
      end
    end

    def handle_missing_events(events)
      return events if events.empty?

      enhanced_events = events.to_a

      # If first event is a check-out, add a predicted check-in at work start time
      if enhanced_events.first.check_out?
        predicted_check_in = ::Attendance::Event.new(
          employee: employee,
          timestamp: settings[:work_start_timestamp],
          event_type: :check_in,
          activity_type: :regular,
          potential_duplicate: false
        )

        # Add to the beginning of the array
        enhanced_events.unshift(predicted_check_in)

        # Create a late period if the first real event is after work start time
        if events.first.timestamp > settings[:work_start_timestamp]
          create_period(
            ::Attendance::Period::PERIOD_TYPES[:late],
            settings[:work_start_timestamp],
            events.first.timestamp,
            nil,
            true,
            "Predicted late period based on missing check-in"
          )
        end
      end

      # If last event is a check-in, add a predicted check-out at work end time
      if enhanced_events.last.check_in?
        predicted_check_out = ::Attendance::Event.new(
          employee: employee,
          timestamp: settings[:work_end_timestamp],
          event_type: :check_out,
          activity_type: :regular,
          potential_duplicate: false
        )

        # Add to the end of the array
        enhanced_events.push(predicted_check_out)

        # Create an early departure period only if it's not an incomplete day
        # and the last real event is before work end time
        if !incomplete_day && events.last.timestamp < settings[:work_end_timestamp]
          create_period(
            ::Attendance::Period::PERIOD_TYPES[:early_departure],
            events.last.timestamp,
            settings[:work_end_timestamp],
            nil,
            true,
            "Predicted early departure period based on missing check-out"
          )
        end
      end

      enhanced_events
    end

    def calculate_work_and_break_periods(events)
      return if events.size < 2

      # Pair events to create periods
      events.each_cons(2) do |event1, event2|
        if event1.check_in? && event2.check_out?
          # Skip creating work periods for early arrival time (before work start time)
          if event1.timestamp < settings[:work_start_timestamp]
            # If the check-out is after work start time, create a work period only for the portion
            # that is during work hours
            if event2.timestamp > settings[:work_start_timestamp]
              create_period(
                ::Attendance::Period::PERIOD_TYPES[:work],
                settings[:work_start_timestamp],
                event2.timestamp,
                event1.activity_type,
                event1.id.nil? || event2.id.nil?, # is_predicted if either event is predicted
                nil
              )
            end
            # Otherwise, don't create a work period as this time is already covered by early_arrival
          else
            # Normal work period (not early arrival)
            create_period(
              ::Attendance::Period::PERIOD_TYPES[:work],
              event1.timestamp,
              event2.timestamp,
              event1.activity_type,
              event1.id.nil? || event2.id.nil?, # is_predicted if either event is predicted
              nil
            )
          end
        elsif event1.check_out? && event2.check_in?
          # Break period
          create_period(
            ::Attendance::Period::PERIOD_TYPES[:break],
            event1.timestamp,
            event2.timestamp,
            event1.activity_type,
            event1.id.nil? || event2.id.nil?, # is_predicted if either event is predicted
            nil
          )
        end
      end
    end

    def calculate_late_and_early_periods(events)
      return if events.empty?

      # Find the first check-in of the day
      first_check_in = events.find(&:check_in?)
      return unless first_check_in

      # If first check-in is before work start time, create an early arrival period
      if first_check_in.timestamp < settings[:work_start_timestamp]
        create_period(
          ::Attendance::Period::PERIOD_TYPES[:early_arrival],
          first_check_in.timestamp,
          settings[:work_start_timestamp],
          nil,
          false,
          "Early arrival"
        )
      # If first check-in is after work start time, create a late period
      elsif first_check_in.timestamp > settings[:work_start_timestamp]
        create_period(
          ::Attendance::Period::PERIOD_TYPES[:late],
          settings[:work_start_timestamp],
          first_check_in.timestamp,
          nil,
          false,
          "Late arrival"
        )
      end

      # Find the last check-out of the day
      last_check_out = events.reverse.find(&:check_out?)
      return unless last_check_out

      # If last check-out is before work end time and it's not an incomplete day,
      # create an early departure period
      if !incomplete_day && last_check_out.timestamp < settings[:work_end_timestamp]
        create_period(
          ::Attendance::Period::PERIOD_TYPES[:early_departure],
          last_check_out.timestamp,
          settings[:work_end_timestamp],
          nil,
          false,
          "Early departure"
        )
      end
    end

    def create_period(type, start_timestamp, end_timestamp, activity_type = nil, is_predicted = false, notes = nil)
      duration_minutes = ((end_timestamp - start_timestamp) / 60).round

      ::Attendance::Period.create!(
        employee: employee,
        date: date,
        period_type: type,
        start_timestamp: start_timestamp,
        end_timestamp: end_timestamp,
        duration_minutes: duration_minutes,
        activity_type: activity_type,
        is_predicted: is_predicted,
        notes: notes
      )
    end
  end
end
