require 'net/http'
require 'json'

module Attendance
  module Adapters
    class GenericHttpAdapter < BaseAdapter
      attr_reader :base_url, :api_key, :timeout, :headers

      def initialize(options = {})
        # Don't call super yet - we need to set our instance variables first
        @device_name = options[:device_name] || 'Unknown Device'
        @device_id = options[:device_id]
        @options = options

        @api_key = options[:api_key] || options[:password]
        @timeout = options[:timeout] || 30
        @headers = options[:headers] || {}

        # Set base URL - prefer explicit base_url, otherwise build from IP/port
        if options[:base_url].present?
          @base_url = options[:base_url]
        elsif options[:device_ip] && options[:device_port]
          protocol = options[:use_https] ? 'https' : 'http'
          @base_url = "#{protocol}://#{options[:device_ip]}:#{options[:device_port]}"
        else
          @base_url = options[:device_ip] # fallback to just the IP/domain
        end

        # Now validate after everything is set up
        validate_required_options!
      end

      # Fetch attendance data from generic HTTP API
      def fetch_data(start_date = Date.yesterday, end_date = Date.today)
        start_date, end_date = validate_date_range(start_date, end_date)
        log_info("Fetching attendance data for #{start_date} to #{end_date}")

        measure_performance("fetch_data") do
          records = []

          # Generic endpoint for attendance data
          endpoint = "/api/attendance/records"
          params = {
            start_date: start_date.iso8601,
            end_date: end_date.iso8601,
            format: 'json'
          }

          response = make_api_request(:get, endpoint, params)

          if response && response['data']
            events = response['data']
            log_debug("Retrieved #{events.count} events from device")

            events.each do |event|
              transformed_record = transform_attendance_record(event)
              records << transformed_record if transformed_record
            end
          end

          log_info("Retrieved #{records.count} attendance records")
          records
        end
      rescue => e
        log_error("Error fetching attendance data", e)
        []
      end

      # Test connection to the device
      def test_connection
        log_info("Testing connection to #{base_url}")

        measure_performance("test_connection") do
          # Try health check endpoint first, fallback to device info
          endpoints = %w[/api/health /api/status /api/device/info]

          endpoints.each do |endpoint|
            begin
              response = make_api_request(:get, endpoint)
              if response
                log_info("Successfully connected via #{endpoint}")
                return true
              end
            rescue => e
              log_debug("Endpoint #{endpoint} failed: #{e.message}")
            end
          end

          false
        end
      rescue => e
        handle_connection_error(e)
      end

      # Get device information
      def get_device_info
        log_info("Getting device information")

        endpoints = %w[/api/device/info /api/info /api/status]

        endpoints.each do |endpoint|
          begin
            response = make_api_request(:get, endpoint)

            if response && response['device']
              device_info = response['device']
              return {
                device_name: device_info['name'] || device_info['deviceName'],
                device_id: device_info['id'] || device_info['deviceId'],
                model: device_info['model'],
                version: device_info['version'] || device_info['firmwareVersion'],
                status: device_info['status']
              }
            elsif response
              # Try to extract info from root level
              return {
                device_name: response['name'] || response['deviceName'],
                version: response['version'],
                status: response['status'] || 'online'
              }
            end
          rescue => e
            log_debug("Endpoint #{endpoint} failed: #{e.message}")
          end
        end

        {}
      rescue => e
        log_error("Error getting device info", e)
        {}
      end

      # Capability methods (configurable via options)
      def supports_real_time?
        options[:supports_real_time] || false
      end

      def supports_user_management?
        options[:supports_user_management] || false
      end

      def supports_clear_data?
        options[:supports_clear_data] || false
      end

      protected

      # Implement base class abstract methods
      def extract_employee_code(event)
        # Try common field names for employee identification
        employee_code = event['employee_code'] ||
                       event['employeeCode'] ||
                       event['employee_id'] ||
                       event['employeeId'] ||
                       event['user_id'] ||
                       event['userId'] ||
                       event['card_number'] ||
                       event['cardNumber']
        validate_employee_code(employee_code)
      end

      def extract_timestamp(event)
        # Try common field names for timestamp
        timestamp = event['timestamp'] ||
                   event['time'] ||
                   event['datetime'] ||
                   event['created_at'] ||
                   event['event_time']
        standardize_timestamp(timestamp) if timestamp
      end

      def extract_status(event)
        # Try common field names for status/event type
        status = event['status'] ||
                event['event_type'] ||
                event['type'] ||
                event['action']

        map_status_to_standard(status) if status
      end

      def validate_required_options!
        raise ArgumentError, "base_url or device_ip is required for Generic HTTP adapter" if base_url.blank?
      end

      private

      def make_api_request(method, endpoint, params = {})
        url = "#{base_url}#{endpoint}"

        case method.to_sym
        when :get
          url += "?" + params.map { |k, v| "#{k}=#{CGI.escape(v.to_s)}" }.join('&') if params.any?
          make_http_request(url, :get)
        when :post
          make_http_request(url, :post, params.to_json)
        else
          raise ArgumentError, "Unsupported HTTP method: #{method}"
        end
      end

      def make_http_request(url, method, body = nil)
        uri = URI(url)

        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = uri.scheme == 'https'
        http.read_timeout = timeout
        http.open_timeout = timeout

        request = case method
                  when :get
                    Net::HTTP::Get.new(uri)
                  when :post
                    Net::HTTP::Post.new(uri)
                  end

        # Set headers
        request['Content-Type'] = 'application/json'
        request['Authorization'] = "Bearer #{api_key}" if api_key.present?
        headers.each { |key, value| request[key] = value }

        request.body = body if body

        log_debug("Making #{method.upcase} request to: #{url}")

        response = http.request(request)

        if response.code.start_with?('2')
          JSON.parse(response.body)
        else
          log_error("HTTP request failed with status: #{response.code}")
          log_error("Response body: #{response.body}")
          nil
        end
      rescue JSON::ParserError => e
        log_error("Failed to parse JSON response", e)
        nil
      rescue => e
        log_error("HTTP request failed", e)
        nil
      end

      # Override base transform method to handle generic HTTP data
      def transform_attendance_record(event)
        employee_code = extract_employee_code(event)
        timestamp = extract_timestamp(event)

        return nil if employee_code.blank? || timestamp.blank?

        {
          employee_code: employee_code,
          timestamp: timestamp,
          status: extract_status(event) || 'undetermined',
          location: device_name,
          source_device_id: device_id,
          raw_data: event
        }
      end
    end
  end
end
