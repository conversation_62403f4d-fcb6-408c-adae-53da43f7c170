module Attendance
  class DuplicateDetectionService
    def self.identify_duplicates(events, threshold_seconds = nil)
      return if events.size <= 1

      # Use default threshold if not provided
      threshold_seconds ||= Setting.attendance_duplicate_threshold_seconds

      # Ensure threshold_seconds is an integer
      threshold_seconds = threshold_seconds.to_i

      # Group events by event_type
      events.group_by(&:event_type).each do |event_type, type_events|
        # Sort by timestamp
        sorted_events = type_events.sort_by(&:timestamp)

        # Check for events that are too close together
        sorted_events.each_with_index do |event, index|
          next if index == 0

          previous_event = sorted_events[index - 1]
          time_difference = event.timestamp - previous_event.timestamp

          if time_difference <= threshold_seconds
            # Flag the later event as a potential duplicate
            event.update(
              potential_duplicate: true,
              notes: "#{event.notes}\n\nFlagged as potential duplicate (#{time_difference} seconds after event ##{previous_event.id}) on #{Time.current}"
            )
          end
        end
      end
    end
  end
end
