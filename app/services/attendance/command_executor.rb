module Attendance
  class CommandExecutor
    def self.execute(device, command_name, parameters = {}, employee = nil)
      # Create execution record
      execution = CommandExecution.create!(
        device: device,
        command_name: command_name,
        parameters: parameters,
        status: :running,
        executed_by: employee,
        started_at: Time.current
      )

      begin
        # Execute command on adapter
        adapter = device.create_adapter

        # Check if adapter supports commands and the specific command
        unless adapter.supports_commands?
          result = Attendance::CommandResult.failure("Device does not support commands")
          execution.update!(
            status: :failed,
            result: result.as_json,
            completed_at: Time.current
          )
          return result
        end

        unless adapter.available_commands.include?(command_name.to_s)
          result = Attendance::CommandResult.failure("Command '#{command_name}' not supported")
          execution.update!(
            status: :failed,
            result: result.as_json,
            completed_at: Time.current
          )
          return result
        end

        # Execute command
        result = adapter.execute_command(command_name, parameters)

        # Update execution record
        execution.update!(
          status: result.success ? :completed : :failed,
          result: result.as_json,
          completed_at: Time.current
        )

        result
      rescue => e
        result = Attendance::CommandResult.failure(e.message)
        execution.update!(
          status: :failed,
          result: result.as_json,
          completed_at: Time.current
        )

        result
      end
    end

    # Get available commands for a device
    def self.available_commands_for_device(device)
      adapter = device.create_adapter

      return [] unless adapter.supports_commands?

      adapter.available_commands.map do |command_name|
        {
          name: command_name,
          display_name: command_name.titleize,
          description: command_description(command_name)
        }
      end
    end

    # Validate command parameters
    def self.validate_command(device, command_name, parameters = {})
      adapter = device.create_adapter

      unless adapter.supports_commands?
        return { valid: false, error: "Device does not support commands" }
      end

      unless adapter.available_commands.include?(command_name.to_s)
        return { valid: false, error: "Command '#{command_name}' not supported" }
      end

      # Add specific parameter validation here if needed
      case command_name.to_s
      when 'write_lcd'
        if parameters[:message].blank?
          return { valid: false, error: "Message parameter is required for write_lcd command" }
        end
        if parameters[:message].length > 100
          return { valid: false, error: "Message must be 100 characters or less" }
        end
      end

      { valid: true }
    end

    private

    # Get description for a command
    def self.command_description(command_name)
      descriptions = {
        'restart' => 'Restart the attendance device',
        'test_voice' => 'Test the device speaker/voice system',
        'clear_logs' => 'Clear all attendance logs from device memory',
        'clear_lcd' => 'Clear the LCD display',
        'write_lcd' => 'Write a message to the LCD display',
        'unlock' => 'Unlock the door/access control',
        'door_state' => 'Get the current door state',
        'poweroff' => 'Power off the device',
        'refresh' => 'Refresh device data and settings'
      }

      descriptions[command_name.to_s] || 'Execute device command'
    end
  end
end

