# frozen_string_literal: true

module Statistics
  module Calculators
    class LeavesTakenCalculator
      include BaseCalculator
      include Statistics::Helpers::DateHelpers
      include Statistics::Helpers::EmployeeHelpers
      include Statistics::Helpers::CardHelpers

      def validate_context(context)
        unless context[:employee_id].present?
          raise ArgumentError, "Missing required context: employee_id must be provided for leaves taken calculator"
        end

        context[:employee] = find_employee(context[:employee_id])
        context[:start_date] = parse_date_with_formats(context[:start_date]) || Date.current.beginning_of_year
        context[:end_date] = parse_date_with_formats(context[:end_date]) || Date.current.end_of_year

        if context[:end_date] < context[:start_date]
          raise ArgumentError, "End date cannot be before start date"
        end

        context[:comparison_period] = (context[:comparison_period] || 'year').to_sym
        context[:comparison_text] = generate_comparison_text(context[:comparison_period])
      rescue ArgumentError => e
        raise ArgumentError, e.message
      rescue => e
        raise ArgumentError, "Invalid date format: #{e.message}"
      end

      def perform_calculation(context)
        employee = context[:employee]
        start_date = context[:start_date]
        end_date = context[:end_date]
        comparison_period = context[:comparison_period]

        current_leaves = count_approved_leaves(employee, start_date, end_date)

        prev_start_date, prev_end_date = calculate_previous_period(start_date, end_date, comparison_period)
        previous_leaves = count_approved_leaves(employee, prev_start_date, prev_end_date)

        comparison_text = context[:comparison_text] || generate_comparison_text(comparison_period)

        create_metric_card(
          card_id,
          'Leaves Taken',
          current_leaves,
          'days',
          current_leaves,
          previous_leaves,
          comparison_text
        )
      end

      private

      def count_approved_leaves(employee, start_date, end_date)
        Leave.where(employee: employee)
             .where("start_date >= ? AND end_date <= ?", start_date, end_date)
             .where(status: :approved)
             .sum(&:duration)
      end
    end
  end
end
