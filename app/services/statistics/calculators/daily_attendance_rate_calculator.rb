# frozen_string_literal: true

module Statistics
  module Calculators
    class DailyAttendanceRateCalculator
      include BaseCalculator
      include Statistics::Helpers::DateHelpers
      include Statistics::Helpers::CardHelpers

      def validate_context(context)
        # This is a system-wide metric, so no employee_id is required
        # Parse date if provided, otherwise use today
        context[:date] = parse_date_with_formats(context[:date]) || Date.today
      rescue => e
        raise ArgumentError, "Invalid date format: #{e.message}"
      end

      def perform_calculation(context)
        date = context[:date]

        # Count employees present today
        present_employees = count_present_employees(date)

        # Count employees present yesterday
        yesterday = date - 1.day
        yesterday = date - 3.days if [ 0, 1 ].include?(yesterday.wday) # Skip weekends
        previous_present = count_present_employees(yesterday)

        # Create and return the card
        create_metric_card(
          card_id,
          'Daily Attendance Rate',
          present_employees,
          'employees',
          present_employees,
          previous_present,
          'compared to previous workday'
        )
      end

      private

      def count_present_employees(date)
        # Count unique employees with attendance records for the given date
        Attendance::Period.where(date: date).select(:employee_id).distinct.count
      end
    end
  end
end
