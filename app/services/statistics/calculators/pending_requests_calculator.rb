# frozen_string_literal: true

module Statistics
  module Calculators
    class PendingRequestsCalculator
      include BaseCalculator
      include Statistics::Helpers::CardHelpers

      def validate_context(context)
        # This is a system-wide metric, so no employee_id is required
        # No specific validation needed
      end

      def perform_calculation(context)
        # Count all pending leave requests
        pending_leaves = Leave.where(status: :pending).count

        # Count all pending approval requests
        pending_approvals = ApprovalRequest.where(status: :pending).count

        # Total pending requests
        total_pending = pending_leaves + pending_approvals

        # Create and return the card
        create_metric_card(
          card_id,
          'Pending Requests',
          total_pending,
          'requests',
          total_pending,
          0, # No comparison for this metric
          'awaiting approval'
        )
      end
    end
  end
end
