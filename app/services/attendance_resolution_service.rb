class AttendanceResolutionService
  def self.resolve_for_date(target_date, employee_count = nil)
    # Get all employees with undetermined events for the target date
    employee_ids = AttendanceEvent.undetermined.for_date(target_date).pluck(:employee_id).uniq

    # Use provided employee_count or calculate it
    employee_count ||= employee_ids.count
    results = { resolved_count: 0, employee_count: employee_count, remaining_undetermined: 0 }

    employee_ids.each_with_index do |employee_id, index|
      employee = Employee.find(employee_id)

      # Count undetermined events before resolution
      undetermined_before = AttendanceEvent.undetermined.where(
        employee_id: employee.id,
        timestamp: target_date.beginning_of_day..target_date.end_of_day
      ).count

      # Resolve undetermined events
      AttendanceEvent.resolve_undetermined_events(employee, target_date)

      # Count undetermined events after resolution
      undetermined_after = AttendanceEvent.undetermined.where(
        employee_id: employee.id,
        timestamp: target_date.beginning_of_day..target_date.end_of_day
      ).count

      resolved_for_employee = undetermined_before - undetermined_after
      results[:resolved_count] += resolved_for_employee

      yield(employee, index, resolved_for_employee, undetermined_after) if block_given?
    end

    # Count remaining undetermined events
    results[:remaining_undetermined] = AttendanceEvent.undetermined.for_date(target_date).count

    results
  end
end
