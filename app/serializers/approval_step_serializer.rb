class ApprovalStepSerializer
  include JSONAPI::Serializer

  attributes :step_id, :name, :sequence, :approval_type, :approver_ids, :created_at, :updated_at

  attribute :complete do |object|
    object.complete?
  end

  attribute :rejected do |object|
    object.rejected?
  end

  attribute :actions do |object|
    object.actions.map do |action|
      {
        id: action.id,
        user_id: action.user_id,
        action: action.action,
        comment: action.comment,
        created_at: action.created_at
      }
    end
  end
end
