# frozen_string_literal: true

module Attendance
  class CommandExecutionSerializer
    include JSONAPI::Serializer

    set_type :command_execution
    set_id :id

    # Core attributes
    attributes :command_name, :parameters, :status, :result, :duration

    # Timing attributes
    attributes :started_at, :completed_at

    attribute :success? do |object|
      object.success?
    end

    attribute :failure? do |object|
      object.failure?
    end

    attribute :in_progress? do |object|
      object.in_progress?
    end

    # Relationships
    belongs_to :device, serializer: DeviceSerializer
    belongs_to :executed_by, serializer: EmployeeSerializer, object_method_name: :executed_by

    # Meta information
    attribute :executed_at do |object|
      object.started_at&.iso8601
    end

    attribute :completed_at_iso do |object|
      object.completed_at&.iso8601
    end
  end
end
