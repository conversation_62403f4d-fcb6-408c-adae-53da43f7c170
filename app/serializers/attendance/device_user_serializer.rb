module Attendance
  class DeviceUserSerializer
    include JSONAPI::Serializer
    set_type :device_user

    # Pure device user attributes from the device (only what's actually available)
    attributes :user_id, :name, :privilege, :password, :group_id, :card_number, :device_user_id

    # Helper attributes based on available data
    attribute :admin? do |object|
      object.admin?
    end

    attribute :user? do |object|
      object.user?
    end

    attribute :has_card? do |object|
      object.has_card?
    end

    attribute :has_password? do |object|
      object.has_password?
    end

    attribute :verification_methods do |object|
      object.verification_methods
    end
  end
end
