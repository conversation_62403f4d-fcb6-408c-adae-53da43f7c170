module Attendance
  class EventSerializer
    include JSONAPI::Serializer

    attributes :timestamp, :event_type, :activity_type,
               :location, :notes, :created_at, :updated_at

    attribute :inferred_type do |event, params|
      if event.undetermined? && params && params[:include_inference]
        time_suggestion = event.infer_event_type_by_time
        sequence_suggestion = event.infer_event_type_by_sequence

        # If both methods agree, that's our strongest suggestion
        if time_suggestion == sequence_suggestion && time_suggestion != 'undetermined'
          time_suggestion
          # If sequence method has a result, prefer it
        elsif sequence_suggestion != 'undetermined'
          sequence_suggestion
          # Otherwise use time-based
        else
          time_suggestion
        end
      else
        nil
      end
    end

    belongs_to :employee, serializer: ::EmployeeSerializer
  end
end
