module Attendance
  class SummarySerializer
    include JSONAPI::Serializer

    attributes :date, :first_check_in, :last_check_out, 
               :total_duration_minutes, :work_status, :event_count, 
               :undetermined_count, :notes, :created_at, :updated_at

    belongs_to :employee, serializer: EmployeeSerializer

    # Computed attributes
    attribute :total_duration_hours do |summary|
      summary.duration_hours
    end

    attribute :formatted_duration do |summary|
      summary.formatted_duration
    end

    attribute :work_status_label do |summary|
      summary.work_status_label
    end

    attribute :has_undetermined_events do |summary|
      summary.has_undetermined_events?
    end

    attribute :status_badge do |summary|
      case summary.work_status
      when 'present'
        { color: 'green', text: 'Present' }
      when 'absent'
        { color: 'red', text: 'Absent' }
      when 'partial'
        { color: 'yellow', text: 'Partial' }
      when 'late'
        { color: 'orange', text: 'Late' }
      when 'early_departure'
        { color: 'purple', text: 'Early Out' }
      else
        { color: 'gray', text: summary.work_status&.humanize || 'Unknown' }
      end
    end

    attribute :first_check_in_formatted do |summary|
      summary.first_check_in&.strftime("%H:%M")
    end

    attribute :last_check_out_formatted do |summary|
      summary.last_check_out&.strftime("%H:%M")
    end

    attribute :work_duration_status do |summary|
      return 'unknown' unless summary.total_duration_minutes

      hours = summary.total_duration_minutes / 60.0
      
      if hours >= 8
        'full_day'
      elsif hours >= 4
        'half_day'
      elsif hours > 0
        'partial'
      else
        'no_work'
      end
    end

    attribute :productivity_score do |summary|
      return 0 unless summary.total_duration_minutes

      # Base score on duration and presence
      duration_score = [summary.total_duration_minutes / 480.0, 1.0].min * 70 # Max 70 points for 8+ hours
      
      # Penalty for undetermined events
      undetermined_penalty = (summary.undetermined_count || 0) * 5
      
      # Bonus for consistent attendance
      consistency_bonus = case summary.work_status
      when 'present' then 30
      when 'partial' then 15
      when 'late' then 10
      else 0
      end
      
      score = duration_score + consistency_bonus - undetermined_penalty
      [score.round, 0].max # Ensure non-negative
    end

    attribute :daily_insights do |summary|
      insights = []
      
      if summary.undetermined_count && summary.undetermined_count > 0
        insights << "#{summary.undetermined_count} undetermined events need resolution"
      end
      
      if summary.total_duration_minutes
        hours = summary.total_duration_minutes / 60.0
        if hours < 4
          insights << "Short work day (#{hours.round(1)} hours)"
        elsif hours > 10
          insights << "Long work day (#{hours.round(1)} hours)"
        end
      end
      
      if summary.work_status == 'late'
        insights << "Late arrival detected"
      end
      
      if summary.first_check_in && summary.last_check_out
        work_span = (summary.last_check_out - summary.first_check_in) / 3600.0 # hours
        actual_work = (summary.total_duration_minutes || 0) / 60.0
        break_time = work_span - actual_work
        
        if break_time > 2
          insights << "Extended breaks (#{break_time.round(1)} hours total)"
        end
      end
      
      insights.empty? ? ["Regular work day"] : insights
    end
  end
end
