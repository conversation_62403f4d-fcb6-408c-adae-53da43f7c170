module Attendance
  class DeviceSerializer
    include JSONAPI::Serializer

    attributes :name, :adapter_type, :ip_address, :port, :location, :status, :last_seen_at,
               :connection_config, :sync_config, :capabilities, :created_at, :updated_at

    # Computed attributes
    attribute :health_score do |device|
      device.sync_health_score
    end

    attribute :last_sync do |device|
      last_sync = device.last_sync_log
      if last_sync
        {
          id: last_sync.id,
          status: last_sync.status,
          started_at: last_sync.started_at,
          completed_at: last_sync.completed_at,
          duration: last_sync.duration_in_words,
          success_rate: last_sync.success_rate,
          records_processed: last_sync.result_summary['total_processed'] || 0,
          records_imported: last_sync.result_summary['success'] || 0
        }
      end
    end

    attribute :last_successful_sync do |device|
      last_successful = device.last_successful_sync
      if last_successful
        {
          id: last_successful.id,
          completed_at: last_successful.completed_at,
          records_imported: last_successful.result_summary['success'] || 0
        }
      end
    end

    attribute :connection_status do |device|
      if device.last_seen_at
        if device.last_seen_at > 1.hour.ago
          'online'
        elsif device.last_seen_at > 24.hours.ago
          'recently_online'
        else
          'offline'
        end
      else
        'unknown'
      end
    end

    attribute :sync_statistics do |device|
      {
        total_syncs: device.sync_logs.count,
        successful_syncs: device.sync_logs.successful.count,
        failed_syncs: device.sync_logs.failed.count,
        success_rate: device.sync_health_score,
        average_sync_time: SyncLog.average_sync_time_for_device(device),
        last_7_days_syncs: device.sync_logs.where('created_at > ?', 7.days.ago).count
      }
    end

    attribute :device_capabilities do |device|
      capabilities = device.capabilities || {}

      # Add dynamic capability detection
      begin
        adapter = device.create_adapter
        capabilities.merge(
          supports_real_time: adapter.supports_real_time?,
          supports_user_management: adapter.supports_user_management?,
          supports_clear_data: adapter.supports_clear_data?
        )
      rescue
        capabilities
      end
    end

    attribute :recent_events_count do |device|
      device.events.where('created_at > ?', 24.hours.ago).count
    end

    # Include sync logs when requested
    has_many :sync_logs, serializer: SyncLogSerializer do |device, params|
      if params && params[:include_sync_logs]
        device.sync_logs.recent.limit(10)
      else
        []
      end
    end

    # Include recent attendance events when requested
    has_many :events, serializer: EventSerializer do |device, params|
      if params && params[:include_recent_events]
        device.events.includes(:employee).recent.limit(20)
      else
        []
      end
    end
  end
end
