class LeaveSerializer
  include JSONAPI::Serializer

  attributes :leave_type, :leave_duration, :status, :start_date, :end_date, :reason, :created_at, :updated_at
  belongs_to :employee

  attribute :duration do |leave|
    leave.duration
  end

  attribute :working_days do |leave|
    leave.send(:working_days)
  end

  attribute :with_deduction do |leave|
    leave.with_deduction?
  end

  has_one :approval_request, serializer: ApprovalRequestSerializer

  has_many :documents, serializer: ActiveStorage::AttachmentSerializer
end
