class SalaryPackageSerializer
  include JSONAPI::Serializer

  attributes :base_salary, :housing_allowance, :transportation_allowance,
             :other_allowances, :effective_date, :end_date,
             :total_package_value, :active,
             :adjustment_reason, :previous_package_id, :notes,
             :created_at, :updated_at

  # Add a virtual attribute to indicate if the package is currently active
  attribute :active do |package|
    package.active?
  end

  belongs_to :employee
end
