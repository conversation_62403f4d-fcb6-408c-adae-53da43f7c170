# frozen_string_literal: true

class Salary::CalculationSuccessSerializer
  include JSONAPI::Serializer

  attributes :employee_id, :calculation_id

  # Add links to related resources
  attribute :links do |object|
    {
      employee: "/api/employees/#{object.employee_id}",
      calculation: "/api/finance/salary_calculations/#{object.calculation_id}"
    }
  end

  # Add relationships
  belongs_to :employee, if: Proc.new { |record| record.employee.present? }
  belongs_to :calculation, serializer: :salary_calculation, if: Proc.new { |record| record.calculation.present? }
end
