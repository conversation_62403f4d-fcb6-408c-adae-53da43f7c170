# frozen_string_literal: true

class Salary::CalculationFailureSerializer
  include JSONAPI::Serializer

  attributes :employee_id, :errors

  # Format errors in a more structured way
  attribute :formatted_errors do |object|
    object.errors.map { |e| { message: e } }
  end

  # Add links to related resources
  attribute :links do |object|
    {
      employee: "/api/employees/#{object.employee_id}"
    }
  end

  # Add relationships
  belongs_to :employee, if: Proc.new { |record| record.employee.present? }
end
