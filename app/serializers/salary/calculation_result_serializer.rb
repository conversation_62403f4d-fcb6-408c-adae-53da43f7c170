# frozen_string_literal: true

class Salary::CalculationResultSerializer
  include JSONAPI::Serializer

  # Basic attributes
  attributes :success_count, :failure_count, :total_count

  # Relationships
  has_many :successes, serializer: Salary::CalculationSuccessSerializer
  has_many :failures, serializer: Salary::CalculationFailureSerializer

  # Include the relationships by default
  set_id { |object| object.object_id.to_s }
end
