class SalaryCalculationDetailSerializer
  include JSONAPI::Serializer

  attributes :id, :detail_type, :category, :amount, :description

  # Include reference information if available
  attribute :reference_info do |object|
    if object.reference.present?
      {
        id: object.reference.id,
        type: object.reference_type,
        # Add any other relevant reference information
      }
    else
      nil
    end
  end
end
