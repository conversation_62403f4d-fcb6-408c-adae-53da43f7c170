module ActiveStorage
  class AttachmentSerializer
    include JSONAPI::Serializer

    # Set the type for JSON:API
    set_type :attachment

    # Define attributes
    attributes :filename, :content_type, :created_at

    attribute :url do |attachment|
      attachment.cdn_url || attachment.url
    end

    attribute :bytes_size do |attachment|
      attachment.blob.byte_size if attachment.blob.present?
    end
  end
end
