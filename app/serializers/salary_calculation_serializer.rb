class SalaryCalculationSerializer
  include JSONAPI::Serializer

  attributes :period, :period_start_date, :period_end_date,
             :gross_salary, :deductions, :net_salary, :status,
             :calculation_date, :payment_date, :notes,
             :total_deductions, :total_hours,
             :created_at, :updated_at

  attribute :has_salary_slip do |calculation|
    calculation.salary_slip_pdf.attached?
  end

  belongs_to :employee
  belongs_to :salary_package
  belongs_to :approved_by, serializer: :employee, if: Proc.new { |record| record.approved_by.present? }

  has_many :calculation_details, if: Proc.new { |_record, params| params && params[:include_details] }

  # Add a method to format the details in a more structured way
  attribute :detailed_breakdown, if: Proc.new { |_record, params| params && params[:include_details] } do |object|
    details = object.calculation_details.group_by(&:detail_type)

    {
      base: details['base'] || [],
      additions: details['addition'] || [],
      deductions: details['deduction'] || [],
      total_additions: (details['addition'] || []).sum(&:amount),
      total_deductions: (details['deduction'] || []).sum(&:amount)
    }
  end

  has_one :approval_request, serializer: ApprovalRequestSerializer
end
