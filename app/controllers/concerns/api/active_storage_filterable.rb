module Api
  module ActiveStorageFilterable
    extend ActiveSupport::Concern

    included do
      include JSONAPI::Filtering
    end

    # Apply filters and sorting to a collection of Active Storage attachments using Ransack
    #
    # @param collection [ActiveStorage::Attached::Many] The collection of attachments
    # @return [ActiveRecord::Relation] The filtered and sorted collection
    def apply_filters(collection)
      # Convert the ActiveStorage::Attached::Many to an ActiveRecord::Relation
      attachments = ActiveStorage::Attachment.where(record: collection.record, name: collection.name)

      # Join with the blobs table to access filename and content_type
      attachments = attachments.joins(:blob)

      # Check if we have any filters to apply
      filter_params = params[:filter]&.dup || {}

      # Process filters if present
      if filter_params.present?
        # Convert JSONAPI filter params to Ransack format
        ransack_params = {}

        # Process all filter parameters in a single loop
        filter_params.keys.dup.each do |key|
          value = filter_params.delete(key)

          # Handle special blob attributes (filename and content_type)
          if key == 'filename' || key == 'content_type'
            # Legacy format without predicates - use 'cont' (contains) as default predicate
            ransack_params["blob_#{key}_cont"] = value
          elsif key.start_with?('filename_', 'content_type_')
            # Format with predicates - convert 'filename_eq' to 'blob_filename_eq', etc.
            attribute, *predicates = key.split('_')
            blob_key = "blob_#{attribute}_#{predicates.join('_')}"
            ransack_params[blob_key] = value
          elsif key.include?('_')
            # Standard attribute with predicate - use as is
            ransack_params[key] = value
          else
            # Standard attribute without predicate - use 'eq' as default predicate
            ransack_params[key + '_eq'] = value
          end
        end

        # Apply Ransack filters
        if ransack_params.present?
          filtered_attachments = attachments.ransack(ransack_params).result
        else
          filtered_attachments = attachments
        end
      else
        filtered_attachments = attachments
      end

      # Apply sorting if present
      if params[:sort].present?
        sort_params = params[:sort].split(',')

        # Process each sort parameter
        sort_fields = sort_params.map do |sort_param|
          if sort_param.start_with?('-')
            # Descending order
            field = sort_param[1..-1] # Remove the leading minus sign

            # Map the field to the appropriate column
            if ['filename', 'content_type'].include?(field)
              { "active_storage_blobs.#{field}" => :desc }
            else
              { field => :desc }
            end
          else
            # Ascending order
            field = sort_param

            # Map the field to the appropriate column
            if ['filename', 'content_type'].include?(field)
              { "active_storage_blobs.#{field}" => :asc }
            else
              { field => :asc }
            end
          end
        end

        # Apply the sort
        sorted_attachments = filtered_attachments
        sort_fields.each do |sort_field|
          sorted_attachments = sorted_attachments.order(sort_field)
        end

        # Return the sorted and filtered attachments
        yield sorted_attachments if block_given?
        sorted_attachments
      else
        # No sorting to apply, just return the filtered attachments
        yield filtered_attachments if block_given?
        filtered_attachments
      end
    end
  end
end
