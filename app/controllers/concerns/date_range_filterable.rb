module DateRangeFilterable
  extend ActiveSupport::Concern

  # Apply date filters to a collection
  def apply_date_filters(collection, date_params)
    if date_params[:date].present?
      collection.where(date: date_params[:date])
    elsif date_params[:start_date].present? && date_params[:end_date].present?
      collection.where(date: date_params[:start_date]..date_params[:end_date])
    else
      collection
    end
  end

  # Parse and validate date parameters
  def parse_date_params
    date_params = {}

    if params[:date].present?
      date_params[:date] = Date.parse(params[:date])
      date_params[:single_date] = true
    elsif params[:start_date].present? && params[:end_date].present?
      date_params[:start_date] = Date.parse(params[:start_date])
      date_params[:end_date] = Date.parse(params[:end_date])
      date_params[:single_date] = false
    end

    date_params
  rescue Date::Error
    raise InvalidDateFormatError
  end

  # Custom error for invalid date formats
  class InvalidDateFormatError < StandardError; end
end
