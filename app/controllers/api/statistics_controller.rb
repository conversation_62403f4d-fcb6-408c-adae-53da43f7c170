# frozen_string_literal: true

module Api
  class StatisticsController < ApplicationController
    before_action :authenticate_session!

    api! "Gets metric cards for dashboard"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :context, Hash, desc: "Context parameters for the calculators"
    param_group :filter_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Gets metric cards for dashboard display.
      Each card has a standardized format with title, value, and comparison data.
      Requires permission: <code>:read, :statistics</code>.

      <b>Examples:</b>

      Get all cards with context:
      <code>GET /api/statistics?context[employee_id]=1</code>

      Get specific cards with context:
      <code>GET /api/statistics?filter[metric_key_in][]=leaves&filter[metric_key_in][]=late_arrivals&context[employee_id]=1</code>

      Get cards with additional context parameters:
      <code>GET /api/statistics?context[employee_id]=1&context[start_date]=2025-01-01&context[end_date]=2025-01-31&context[comparison_period]=last_year</code>
    HTML
    )
    returns code: 200, desc: "List of metric cards"

    def index
      # Get requested card types (nil means all cards)
      requested_cards = params.dig(:filter, :metric_key_in)&.map(&:to_sym)

      service = Statistics::MetricCardService.new
      cards = service.get_cards(get_context, requested_cards)

      serialize_response(cards)
    end

    private

    def get_context
      # Extract the context hash from params
      context_params = params[:context] || {}

      # Convert to a symbolized hash
      # Handle both ActionController::Parameters and Hash objects
      if context_params.respond_to?(:to_unsafe_h)
        context_params.to_unsafe_h.symbolize_keys
      else
        context_params.symbolize_keys
      end
    end
  end
end
