module Api
  class EmployeeAttachmentsController < ApplicationController
    include Api::ActiveStorageFilterable

    before_action :authenticate_session!
    before_action :set_employee
    before_action :set_attachment, only: [ :show, :update, :destroy ]
    before_action :authorize_read, only: [ :index, :show ]
    before_action :authorize_update, only: [ :create, :update, :destroy ]

    api! "Lists all attachments for an employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :employee_id, Integer, required: true, desc: "ID of the employee"
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Lists all attachments for the specified employee.
      Supports filtering by filename, content_type, and created_at.
      Supports sorting with sort=field or sort=-field (descending).
      Supports pagination with page[number] and page[size].
      Requires permission: <code>:read, :employee</code>.
    HTML
    )
    returns code: 200, desc: "List of attachments"

    def index
      apply_filters(@employee.attachments) do |filtered_attachments|
        records, meta = paginate(filtered_attachments)
        serialize_response(records, meta: meta)
      end
    end

    api! "Shows a specific attachment for an employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :employee_id, Integer, required: true, desc: "ID of the employee"
    param :id, Integer, required: true, desc: "ID of the attachment"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Shows details for a specific attachment.
      Requires permission: <code>:read, :employee</code>.
    HTML
    )
    returns code: 200, desc: "Attachment details"
    error code: 404, desc: "Attachment not found"

    def show
      if @attachment
        serialize_response(@attachment)
      else
        serialize_errors({ detail: "Attachment not found" }, :not_found)
      end
    end

    api! "Uploads an attachment for an employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :employee_id, Integer, required: true, desc: "ID of the employee"
    param :attachment, ActionDispatch::Http::UploadedFile, required: true, desc: "File to attach to the employee"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Uploads and attaches a file to the specified employee.
      Requires permission: <code>:update, :employee</code>.
    HTML
    )
    returns code: 201, desc: "Attachment uploaded successfully"
    error code: 422, desc: "No file provided or upload failed"

    def create
      uploaded_file = params.require(:attachment)

      if uploaded_file.present?
        # Use the direct attach method
        attachment = @employee.attachments.attach(
          io: uploaded_file,
          filename: uploaded_file.original_filename,
          content_type: uploaded_file.content_type
        ).last

        # Return the response using the serializer
        serialize_response(attachment, status: :created)
      else
        serialize_errors({ detail: "No file provided." }, :unprocessable_entity)
      end
    end

    api! "Deletes an attachment from an employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :employee_id, Integer, required: true, desc: "ID of the employee"
    param :id, Integer, required: true, desc: "ID of the attachment to delete"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes an attachment from the specified employee.
      Requires permission: <code>:update, :employee</code>.
    HTML
    )
    returns code: 204, desc: "Attachment deleted successfully"
    error code: 404, desc: "Attachment not found"

    def destroy
      if @attachment
        # Purge the attachment (this is the standard way to delete attachments in Active Storage)
        @attachment.purge

        # Return success response
        head :no_content
      else
        serialize_errors({ detail: "Attachment not found" }, :not_found)
      end
    end

    api! "Renames an attachment for an employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :employee_id, Integer, required: true, desc: "ID of the employee"
    param :id, Integer, required: true, desc: "ID of the attachment to rename"
    param :attachment, Hash, required: true, desc: "Attachment attributes" do
      param :filename, String, required: true, desc: "New filename for the attachment"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Renames an existing attachment for the specified employee without changing the file content.
      If only a base filename is provided without an extension, the original file extension will be preserved.
      Requires permission: <code>:update, :employee</code>.
    HTML
    )
    returns code: 200, desc: "Attachment renamed successfully"
    error code: 404, desc: "Attachment not found"
    error code: 422, desc: "No filename provided or update failed"

    def update
      if @attachment.nil?
        return serialize_errors({ detail: "Attachment not found" }, :not_found)
      end

      # Get the new filename from nested parameters
      attachment_params = params.require(:attachment).permit(:filename)
      new_filename = attachment_params[:filename]

      if new_filename.present?
        # Get the blob associated with the attachment
        blob = @attachment.blob

        # Preserve the file extension if not provided in the new filename
        unless new_filename.include?(".")
          original_extension = File.extname(blob.filename.to_s)
          new_filename = "#{new_filename}#{original_extension}"
        end

        # Update the blob's filename
        if blob.update(filename: new_filename)
          # Return the updated attachment
          serialize_response(@attachment)
        else
          # Return errors if the update failed
          serialize_errors({ detail: "Failed to update filename." }, :unprocessable_entity)
        end
      else
        # No filename provided
        serialize_errors({ detail: "A new filename must be provided in attachment[filename]." }, :unprocessable_entity)
      end
    end

    private

    def set_employee
      @employee = Employee.find(params[:employee_id])
    rescue ActiveRecord::RecordNotFound
      render json: { error: "Employee not found" }, status: :not_found
    end

    def set_attachment
      @attachment = @employee.attachments.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      @attachment = nil
    end

    # Authorization methods
    def authorize_read
      if can?(:read, :employee) ||
         (can?(:read_own, :employee) && is_own_employee?)
        true
      else
        render_forbidden("You don't have permission to view this employee's attachments")
        false
      end
    end

    def authorize_update
      if can?(:update, :employee) ||
         (can?(:update_own, :employee) && is_own_employee?)
        true
      else
        render_forbidden("You don't have permission to modify this employee's attachments")
        false
      end
    end

    def is_own_employee?
      @employee&.id == current_employee&.id
    end

    def render_forbidden(message)
      serialize_errors({ detail: message }, :forbidden)
    end
  end
end
