module Api
  class LeavesController < ApplicationController
    before_action :authenticate_session!
    before_action :set_leave, only: [ :show, :update, :destroy, :withdraw ]
    before_action :move_url_ids_to_leave_params, only: [ :index, :create, :update ]
    before_action :authorize_read_all_or_own, only: [ :index ]
    before_action :authorize_read_specific, only: [ :show ]
    before_action :authorize_withdraw, only: [ :withdraw ]

    api! "Lists all leaves"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Lists leaves based on the user's permissions:
      — Users with <code>:read, :leave</code> permission can see all leaves
      — Users with <code>:manage_own, :leave</code> permission can only see their own leaves

      Supports filtering by employee_id, leave_type, start_date, end_date, and reason.
      Supports sorting with sort=field or sort=-field (descending).
      Supports pagination with page[number] and page[size].
    HTML
    )
    returns code: 200, desc: "List of leaves"

    def index
      apply_filters(@collection) do |filtered_leaves|
        records, meta = paginate(filtered_leaves)
        serialize_response(records, meta: meta)
      end
    end

    api! "Shows a specific leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the leave"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Shows details for a specific leave.
      — Users with <code>:read, :leave</code> permission can see any leave
      — Users with <code>:manage_own, :leave</code> permission can only see their own leaves
    HTML
    )
    returns code: 200, desc: "Leave details"
    error code: 404, desc: "Leave not found"

    def show
      if @leave
        serialize_response(@leave, include: [ "approval_request" ])
      else
        serialize_errors({ detail: "Leave not found" }, :not_found)
      end
    end

    api! "Creates a new leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :leave, Hash, required: true, desc: "Leave attributes" do
      param :employee_id, Integer, required: true, desc: "ID of the employee (can be provided here or in URL)"
      param :leave_type, String, required: true, desc: "Type of leave (annual, sick, marriage, maternity, paternity, unpaid)"
      param :leave_duration, String, required: true, desc: "Duration type of leave (full_day, half_day_morning, half_day_afternoon)"
      param :status, String, desc: "Status of the leave (pending, approved, rejected, withdrawn) - default: pending"
      param :start_date, Date, required: true, desc: "Start date of leave"
      param :end_date, Date, required: true, desc: "End date of leave"
      param :reason, String, desc: "Reason for leave"
      param :documents, Array, desc: "Documents to attach to the leave" do
        param :file, ActionDispatch::Http::UploadedFile, desc: "Document file"
      end
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new leave record.
      Requires permission: <code>:create, :leave</code>.
    HTML
    )
    returns code: 201, desc: "Leave created successfully"
    error code: 422, desc: "Validation errors"

    def create
      return unless authorize!(:create, :leave)

      @leave = Leave.new(leave_params)

      if @leave.save
        serialize_response(@leave, status: :created, include: %w[approval_request documents])
      else
        serialize_errors(@leave.errors)
      end
    end

    api! "Updates a leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the leave"
    param :leave, Hash, required: true, desc: "Leave attributes" do
      param :employee_id, Integer, desc: "ID of the employee (can be provided here or in URL)"
      param :leave_type, String, desc: "Type of leave (annual, sick, marriage, maternity, paternity, unpaid)"
      param :leave_duration, String, desc: "Duration type of leave (full_day, half_day_morning, half_day_afternoon)"
      param :status, String, desc: "Status of the leave (pending, approved, rejected, withdrawn)"
      param :start_date, Date, desc: "Start date of leave"
      param :end_date, Date, desc: "End date of leave"
      param :reason, String, desc: "Reason for leave"
      param :documents, Array, desc: "Documents to attach to the leave" do
        param :file, ActionDispatch::Http::UploadedFile, desc: "Document file"
      end
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing leave record.
      Requires permission: <code>:update, :leave</code>.
    HTML
    )
    returns code: 200, desc: "Leave updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      return unless authorize!(:update, :leave)

      if @leave.update(leave_params)
        serialize_response(@leave, include: [ "approval_request" ])
      else
        serialize_errors(@leave.errors)
      end
    end

    api! "Deletes a leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the leave"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a leave by ID.
      Requires permission: <code>:destroy, :leave</code>.
    HTML
    )
    returns code: 204, desc: "Leave deleted successfully"

    def destroy
      return unless authorize!(:destroy, :leave)

      @leave.destroy!
      head :no_content
    end

    api! "Withdraws a leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the leave"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Withdraws a leave by ID. This will cancel any associated approval request.
      Only pending or approved leaves can be withdrawn.
      Requires permission: <code>:update, :leave</code> or <code>:manage_own, :leave</code> for own leaves.
    HTML
    )
    returns code: 200, desc: "Leave withdrawn successfully"
    error code: 422, desc: "Validation errors"

    def withdraw
      @leave.actor_user_id = current_user.id
      if @leave.update(status: :withdrawn)
        serialize_response(@leave.reload, include: [ "approval_request" ])
      else
        serialize_errors(@leave.errors)
      end
    end

    private

    def set_leave
      @leave = Leave.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      @leave = nil
    end

    def leave_params
      params.require(:leave).permit(
        :employee_id,
        :leave_type,
        :leave_duration,
        :start_date,
        :end_date,
        :reason,
        :status,
        documents: []
      )
    end

    def move_url_ids_to_leave_params
      return unless params[:employee_id].present? && params[:leave].present?
      params[:leave][:employee_id] = params[:employee_id].to_i
    end

    def authorize_read_all_or_own
      if can?(:read, :leave)
        @collection = Leave.all
      elsif can?(:manage_own, :leave)
        @collection = Leave.where(employee_id: current_user.id)
      else
        render_forbidden("You don't have permission to view leaves")
        false
      end
    end

    def authorize_read_specific
      unless can?(:read, :leave) ||
             (can?(:manage_own, :leave) && @leave&.employee_id == current_user.id)
        render_forbidden("You don't have permission to view this leave")
        false
      end
    end

    def authorize_withdraw
      unless can?(:update, :leave) ||
             (can?(:manage_own, :leave) && @leave&.employee_id == current_employee.id)
        render_forbidden("You don't have permission to withdraw this leave")
        false
      end
    end

    def render_forbidden(message)
      serialize_errors({ detail: message }, :forbidden)
    end
  end
end
