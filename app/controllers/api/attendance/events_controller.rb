module Api
  module Attendance
    class EventsController < ApplicationController
      before_action :authenticate_session!
      before_action :set_attendance_event, only: [:show, :update, :destroy]
      before_action :set_collection, only: [:index, :undetermined]
      before_action :authorize_read, only: [:index]
      before_action :authorize_read_specific, only: [:show]
      before_action :authorize_create, only: [:create, :batch_create]
      before_action :authorize_update, only: [:update]
      before_action :authorize_destroy, only: [:destroy]
      before_action :authorize_manage, only: [:undetermined]


      api! "Lists attendance events"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :employee_id, Integer, desc: "Filter by employee ID"
      param :date, Date, desc: "Filter by specific date"
      param :start_date, Date, desc: "Filter by date range (start)"
      param :end_date, Date, desc: "Filter by date range (end)"
      param :event_type, String, desc: "Filter by event type (check_in, check_out, undetermined)"
      param :activity_type, String, desc: "Filter by activity type"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists attendance events with filtering options.
        Requires permission: <code>:read, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "List of attendance events"

      def index
        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)
          serialize_response(records, meta: meta, serializer: ::Attendance::EventSerializer)
        end
      end

      api! "Shows a specific attendance event"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance event"
      param_group :include_params
      param_group :fields_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Shows details for a specific attendance event.
        Requires permission: <code>:read, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "Attendance event details"
      error code: 404, desc: "Attendance event not found"

      def show
        serialize_response(@attendance_event, serializer: ::Attendance::EventSerializer)
      end

      api! "Creates a new attendance event"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :attendance_event, Hash, required: true, desc: "Attendance event attributes" do
        param :employee_id, Integer, required: true, desc: "ID of the employee"
        param :timestamp, Integer, required: true, desc: "Timestamp of the event"
        param :event_type, String, desc: "Type of event (check_in, check_out, undetermined)"
        param :activity_type, String, desc: "Type of activity (break, lunch, meeting, business_trip, work_from_home, remote, training, personal_errand)"
        param :location, String, desc: "Location"
        param :notes, String, desc: "Additional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Creates a new attendance event.
        Requires permission: <code>:create, :attendance_event</code>.
      HTML
      )
      returns code: 201, desc: "Attendance event created successfully"
      error code: 422, desc: "Validation errors"

      def create
        @attendance_event = ::Attendance::Event.new(attendance_event_params)

        if @attendance_event.save
          # Update the daily summary
          date = @attendance_event.timestamp_date
          ::Attendance::Summary.recalculate_for(@attendance_event.employee, date)

          serialize_response(@attendance_event, status: :created, serializer: ::Attendance::EventSerializer)
        else
          serialize_errors(@attendance_event.errors)
        end
      end

      api! "Updates an attendance event"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance event"
      param :attendance_event, Hash, required: true, desc: "Attendance event attributes" do
        param :timestamp, Integer, desc: "Timestamp of the event"
        param :event_type, String, desc: "Type of event (check_in, check_out, undetermined)"
        param :activity_type, String, desc: "Type of activity"
        param :location, String, desc: "Location"
        param :notes, String, desc: "Additional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Updates an existing attendance event.
        Requires permission: <code>:update, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "Attendance event updated successfully"
      error code: 422, desc: "Validation errors"

      def update
        old_date = @attendance_event.timestamp_date

        if @attendance_event.update(attendance_event_params)
          # Update the daily summary for both the old and new date if they differ
          new_date = @attendance_event.timestamp_date

          ::Attendance::Summary.recalculate_for(@attendance_event.employee, old_date)
          ::Attendance::Summary.recalculate_for(@attendance_event.employee, new_date) if old_date != new_date

          serialize_response(@attendance_event, serializer: ::Attendance::EventSerializer)
        else
          serialize_errors(@attendance_event.errors)
        end
      end

      api! "Deletes an attendance event"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance event"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Deletes an attendance event by ID.
        Requires permission: <code>:destroy, :attendance_event</code>.
      HTML
      )
      returns code: 204, desc: "Attendance event deleted successfully"

      def destroy
        employee = @attendance_event.employee
        date = @attendance_event.timestamp_date

        @attendance_event.destroy!

        # Update the daily summary
        ::Attendance::Summary.recalculate_for(employee, date)

        head :no_content
      end

      api! "Batch create attendance events"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :batch_events, Hash, required: true, desc: "Batch event attributes" do
        param :events, Array, of: Hash, required: true, desc: "Array of event data" do
          param :employee_id, Integer, required: true, desc: "ID of the employee"
          param :timestamp, Integer, required: true, desc: "Timestamp of the event"
          param :event_type, String, desc: "Type of event (check_in, check_out, undetermined)"
          param :activity_type, String, desc: "Type of activity"
          param :location, String, desc: "Location"
          param :notes, String, desc: "Additional notes"
        end
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Creates multiple attendance events in a single request.
        Useful for importing data from attendance machines.
        Requires permission: <code>:create, :attendance_event</code>.
      HTML
      )
      returns code: 201, desc: "Batch events created successfully"
      error code: 422, desc: "Validation errors"

      def batch_create
        events_params = params.require(:batch_events).require(:events)
        results = { success: [], failure: [] }
        affected_summaries = {}

        events_params.each do |event_params|
          event = ::Attendance::Event.new(event_params.permit(
            :employee_id, :timestamp, :event_type, :activity_type, :location, :notes
          ))

          if event.save
            results[:success] << { id: event.id, employee_id: event.employee_id }

            # Track which summaries need to be updated
            date = event.timestamp_date
            affected_summaries[event.employee_id] ||= Set.new
            affected_summaries[event.employee_id] << date
          else
            results[:failure] << {
              employee_id: event_params[:employee_id],
              timestamp: event_params[:timestamp],
              errors: event.errors.full_messages
            }
          end
        end

        # Update all affected summaries
        affected_summaries.each do |employee_id, dates|
          employee = Employee.find(employee_id)
          dates.each do |date|
            ::Attendance::Summary.recalculate_for(employee, date)
          end
        end

        render json: results, status: :created
      end

      api! "Lists undetermined attendance events"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :employee_id, Integer, desc: "Filter by employee ID"
      param :date, Date, desc: "Filter by specific date"
      param :start_date, Date, desc: "Filter by date range (start)"
      param :end_date, Date, desc: "Filter by date range (end)"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists attendance events with undetermined type for manual resolution.
        Requires permission: <code>:manage, :attendance_event</code>.
      HTML
      )
      returns code: 200, desc: "List of undetermined attendance events"

      def undetermined
        @collection = @collection.where(event_type: :undetermined)

        apply_filters(@collection) do |filtered_and_sorted|
          records, meta = paginate(filtered_and_sorted)
          serialize_response(records, meta: meta, serializer: ::Attendance::EventSerializer)
        end
      end

      private

      def set_attendance_event
        @attendance_event = ::Attendance::Event.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Attendance event not found" }, :not_found)
      end

      def set_collection
        @collection = ::Attendance::Event.includes(:employee)
      end

      def attendance_event_params
        params.require(:attendance_event).permit(
          :employee_id, :timestamp, :event_type, :activity_type, :location, :notes
        )
      end

      # Authorization methods
      def authorize_read
        authorize!(:read, :attendance_event)
      end

      def authorize_read_specific
        authorize!(:read, @attendance_event || :attendance_event)
      end

      def authorize_create
        authorize!(:create, :attendance_event)
      end

      def authorize_update
        authorize!(:update, @attendance_event)
      end

      def authorize_destroy
        authorize!(:destroy, @attendance_event)
      end

      def authorize_manage
        authorize!(:manage, :attendance_event)
      end


    end
  end
end
