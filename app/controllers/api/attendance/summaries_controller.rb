module Api
  module Attendance
    class SummariesController < ApplicationController

      before_action :authenticate_session!
      before_action :set_attendance_summary, only: [:show]

      api! "Lists attendance summaries"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param :employee_id, Integer, desc: "Filter by employee ID"
      param :date, Date, desc: "Filter by specific date"
      param :start_date, Date, desc: "Filter by date range (start)"
      param :end_date, Date, desc: "Filter by date range (end)"
      param :work_status, String, desc: "Filter by work status"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists daily attendance summaries with filtering options.
        Requires permission: <code>:read, :attendance</code>.
      HTML
      )
      returns code: 200, desc: "List of attendance summaries"

      def index
        return unless authorize!(:read, :attendance)

        collection = if params[:employee_id]
                       Employee.find(params[:employee_id]).attendance_summaries
                     else
                       ::Attendance::Summary.all
                     end

        # Apply date filters
        if params[:date]
          collection = collection.where(date: params[:date])
        elsif params[:start_date] && params[:end_date]
          collection = collection.where(date: params[:start_date]..params[:end_date])
        end

        # Apply work status filter
        if params[:work_status]
          collection = collection.where(work_status: params[:work_status])
        end

        apply_filters(collection) do |filtered_summaries|
          records, meta = paginate(filtered_summaries)
          serialize_response(records, meta: meta, serializer: ::Attendance::SummarySerializer)
        end
      end

      api! "Shows a specific attendance summary"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the attendance summary"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Shows details for a specific attendance summary.
        Requires permission: <code>:read, :attendance</code>.
      HTML
      )
      returns code: 200, desc: "Attendance summary details"
      error code: 404, desc: "Attendance summary not found"

      def show
        return unless authorize!(:read, :attendance)

        serialize_response(@attendance_summary, serializer: ::Attendance::SummarySerializer)
      end

      api! "Recalculates attendance summary for a specific date"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :recalculation, Hash, required: true, desc: "Recalculation parameters" do
        param :employee_id, Integer, desc: "ID of the employee (optional - if not provided, recalculates for all employees)"
        param :date, Date, desc: "Specific date to recalculate (optional)"
        param :start_date, Date, desc: "Start date for range recalculation (optional)"
        param :end_date, Date, desc: "End date for range recalculation (optional)"
      end
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Recalculates attendance summaries for specified employees and dates.
        Requires permission: <code>:update, :attendance</code>.
      HTML
      )
      returns code: 200, desc: "Attendance summary recalculated successfully"
      error code: 404, desc: "Employee not found"

      def recalculate
        return unless authorize!(:update, :attendance)

        recalc_params = recalculation_params

        if recalc_params[:date].present?
          # Single date recalculation
          date = recalc_params[:date]
          
          if recalc_params[:employee_id].present?
            # Recalculate for specific employee and date
            employee = Employee.find(recalc_params[:employee_id])
            summary = ::Attendance::Summary.recalculate_for(employee, date)
            serialize_response(summary, serializer: ::Attendance::SummarySerializer)
          else
            # Recalculate for all employees on this date
            ::Attendance::Summary.recalculate_for_all_employees(date)
            render json: {
              message: "Attendance summaries recalculated for all employees on #{date}",
              date: date
            }
          end
        elsif recalc_params[:start_date].present? && recalc_params[:end_date].present?
          # Date range recalculation
          start_date = recalc_params[:start_date]
          end_date = recalc_params[:end_date]
          
          if recalc_params[:employee_id].present?
            # Recalculate for specific employee and date range
            employee = Employee.find(recalc_params[:employee_id])
            ::Attendance::Summary.recalculate_for_date_range(employee, start_date, end_date)
            render json: {
              message: "Attendance summaries recalculated for employee #{employee.name} from #{start_date} to #{end_date}",
              employee_id: employee.id,
              start_date: start_date,
              end_date: end_date
            }
          else
            # Recalculate for all employees in date range
            (start_date..end_date).each do |date|
              ::Attendance::Summary.recalculate_for_all_employees(date)
            end
            render json: {
              message: "Attendance summaries recalculated for all employees from #{start_date} to #{end_date}",
              start_date: start_date,
              end_date: end_date
            }
          end
        else
          return serialize_errors({ detail: "Must provide either date or start_date and end_date." }, :unprocessable_entity)
        end

      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Employee not found" }, :not_found)
      rescue ArgumentError => e
        serialize_errors({ detail: "Invalid date format: #{e.message}" }, :unprocessable_entity)
      end

      api! "Gets attendance summary statistics"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, desc: "Filter by employee ID"
      param :start_date, Date, desc: "Start date for statistics"
      param :end_date, Date, desc: "End date for statistics"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Gets attendance summary statistics for specified period.
        Requires permission: <code>:read, :attendance</code>.
      HTML
      )
      returns code: 200, desc: "Attendance summary statistics"

      def statistics
        return unless authorize!(:read, :attendance)

        start_date = params[:start_date] || Date.current.beginning_of_month
        end_date = params[:end_date] || Date.current

        collection = ::Attendance::Summary.for_date_range(start_date, end_date)
        collection = collection.for_employee(params[:employee_id]) if params[:employee_id]

        stats = {
          total_summaries: collection.count,
          present_days: collection.where(work_status: 'present').count,
          absent_days: collection.where(work_status: 'absent').count,
          partial_days: collection.where(work_status: 'partial').count,
          late_days: collection.where(work_status: 'late').count,
          total_hours: collection.sum(:total_duration_minutes) / 60.0,
          average_hours_per_day: collection.average(:total_duration_minutes).to_f / 60.0,
          undetermined_events: collection.sum(:undetermined_count)
        }

        render json: {
          data: {
            period: {
              start_date: start_date,
              end_date: end_date
            },
            statistics: stats
          }
        }
      end

      private

      def set_attendance_summary
        @attendance_summary = ::Attendance::Summary.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Attendance summary not found" }, :not_found)
      end

      def recalculation_params
        params.require(:recalculation).permit(:employee_id, :date, :start_date, :end_date)
      end
    end
  end
end
