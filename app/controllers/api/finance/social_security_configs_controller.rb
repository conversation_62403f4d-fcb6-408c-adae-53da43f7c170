module Api
  module Finance
    class SocialSecurityConfigsController < ApplicationController
      before_action :authenticate_session!
      before_action :authorize_finance!
      before_action :set_social_security_config, only: [:show, :update, :destroy]

      # GET /api/finance/social_security_configs
      def index
        collection = SocialSecurityConfig.all

        apply_filters(collection) do |filtered_collection|
          records, meta = paginate(filtered_collection)
          serialize_response(records, meta: meta)
        end
      end

      # GET /api/finance/social_security_configs/:id
      def show
        serialize_response(@social_security_config)
      end

      # POST /api/finance/social_security_configs
      def create
        @social_security_config = SocialSecurityConfig.new(social_security_config_params)

        if @social_security_config.save
          serialize_response(@social_security_config, status: :created)
        else
          serialize_errors(@social_security_config.errors)
        end
      end

      # PATCH/PUT /api/finance/social_security_configs/:id
      def update
        if @social_security_config.update(social_security_config_params)
          serialize_response(@social_security_config)
        else
          serialize_errors(@social_security_config.errors)
        end
      end

      # DELETE /api/finance/social_security_configs/:id
      def destroy
        @social_security_config.destroy
        head :no_content
      end

      private

      def set_social_security_config
        @social_security_config = SocialSecurityConfig.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Social security configuration not found" }, :not_found)
        false # Return false to halt the action chain
      end

      def social_security_config_params
        params.require(:social_security_config).permit(
          :employee_rate,
          :employer_rate,
          :max_salary,
          :effective_date,
          :end_date
        )
      end

      def authorize_finance!
        authorize!(:manage_social_security_configs, :salary)
      end
    end
  end
end
