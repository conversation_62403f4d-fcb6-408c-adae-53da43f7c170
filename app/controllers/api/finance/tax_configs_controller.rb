module Api
  module Finance
    class TaxConfigsController < ApplicationController
      before_action :authenticate_session!
      before_action :authorize_finance!
      before_action :set_tax_config, only: [ :show, :update, :destroy ]

      # GET /api/finance/tax_configs
      def index
        collection = TaxConfig.all

        apply_filters(collection) do |filtered_collection|
          records, meta = paginate(filtered_collection)
          serialize_response(records, meta: meta)
        end
      end

      # GET /api/finance/tax_configs/:id
      def show
        serialize_response(@tax_config)
      end

      # POST /api/finance/tax_configs
      def create
        @tax_config = TaxConfig.new(tax_config_params)

        if @tax_config.save
          serialize_response(@tax_config, status: :created)
        else
          serialize_errors(@tax_config.errors)
        end
      end

      # PATCH/PUT /api/finance/tax_configs/:id
      def update
        if @tax_config.update(tax_config_params)
          serialize_response(@tax_config)
        else
          serialize_errors(@tax_config.errors)
        end
      end

      # DELETE /api/finance/tax_configs/:id
      def destroy
        @tax_config.destroy
        head :no_content
      end

      private

      def set_tax_config
        @tax_config = TaxConfig.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        serialize_errors({ detail: "Tax configuration not found" }, :not_found)
        false # Return false to halt the action chain
      end

      def tax_config_params
        params.require(:tax_config).permit(
          :name,
          :effective_date,
          :end_date,
          config_data: {}
        )
      end

      def authorize_finance!
        authorize!(:manage_tax_configs, :salary)
      end
    end
  end
end
