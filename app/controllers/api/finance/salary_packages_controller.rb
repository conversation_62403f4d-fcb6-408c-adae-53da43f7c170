module Api
  module Finance
    class SalaryPackagesController < ApplicationController
      before_action :authenticate_session!
      before_action :set_salary_package, only: [ :show ]
      before_action :move_url_ids_to_salary_package_params, only: [ :index, :create, :show ]
      before_action :set_employee, only: [ :create ]
      before_action :authorize_read_all_or_own, only: [ :index ]
      # before_action :authorize_read_specific, only: [ :show ]
      # before_action :authorize_manage, only: [ :create ]
      # before_action :authorize_create, only: [ :create ]

      api! "Lists salary packages"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, desc: "Filter by employee ID"
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param_group :include_params
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Lists salary packages based on the user's permissions:
      — Users with <code>:read, :salary_package</code> permission can see all salary packages
      — Users with <code>:read_own, :salary_package</code> permission can only see their own salary packages

      Supports filtering by employee_id, base_salary, and effective_date.
      Supports sorting with sort=field or sort=-field (descending).
      Supports pagination with page[number] and page[size].
      HTML
      )
      returns code: 200, desc: "List of salary packages"

      def index
        apply_filters(@collection) do |filtered_packages|
          records, meta = paginate(filtered_packages)
          serialize_response(records, meta: meta)
        end
      end

      api! "Shows a specific salary package"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary package"
      param :employee_id, Integer, required: true, desc: "ID of the employee"
      param_group :include_params
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Shows details for a specific salary package.
      — Users with <code>:read, :salary_package</code> permission can see any salary package
      — Users with <code>:read_own, :salary_package</code> permission can only see their own salary packages
      HTML
      )
      returns code: 200, desc: "Salary package details"
      error code: 404, desc: "Salary package not found"

      def show
        serialize_response(@salary_package)
      end

      api! "Creates a new salary package"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :salary_package, Hash, required: true, desc: "Salary package attributes" do
        param :employee_id, Integer, required: true, desc: "ID of the employee"
        param :base_salary, Float, required: true, desc: "Base salary amount"
        param :housing_allowance, Float, desc: "Housing allowance amount"
        param :transportation_allowance, Float, desc: "Transportation allowance amount"
        param :other_allowances, Float, desc: "Other allowances amount"
        param :effective_date, Date, required: true, desc: "Date when the salary package becomes effective"
        param :notes, String, desc: "Additional notes"
      end
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new salary package for an employee.
      Requires permission: <code>:manage, :salary_package</code>.

      When creating a new salary package, any existing packages that would overlap with the
      new package's effective date will have their end date set to the day before the new package starts.

      Special cases:
      - If a package with the same effective date exists and has no salary calculations,
        the existing package will be updated instead of creating a new one.
      - If a package with the same effective date exists and has salary calculations,
        an error will be returned.
      HTML
      )
      returns code: 201, desc: "Salary package created successfully"
      error code: 422, desc: "Validation errors"

      def create
        begin
          SalaryPackage.transaction do
            @salary_package, success = SalaryPackage.create_with_workflow(@employee, salary_package_params)

            if success
              serialize_response(@salary_package, status: :created)
            else
              serialize_errors(@salary_package.errors)
              raise ActiveRecord::Rollback
            end
          end
        rescue => e
          serialize_errors({ detail: e.message })
        end
      end

      api! "Deletes a salary package"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary package"
      param :employee_id, Integer, required: true, desc: "ID of the employee"
      description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a salary package by ID.
      Requires permission: <code>:manage, :salary_package</code>.

      Note: Deleting salary packages is generally not recommended as it breaks
      historical records. Consider deactivating packages instead.
      HTML
      )
      returns code: 204, desc: "Salary package deleted successfully"
      error code: 404, desc: "Salary package not found"

      # def destroy
      #   return unless authorize!(:manage, :salary_package)
      #
      #   @salary_package.destroy!
      #   head :no_content
      # end

      private

      def set_salary_package
        @salary_package = SalaryPackage.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        @salary_package = nil
      end

      def set_employee
        @employee = Employee.find(params[:salary_package][:employee_id])
      end

      def salary_package_params
        params.require(:salary_package).permit(
          :transportation_allowance,
          :adjustment_reason,
          :housing_allowance,
          :other_allowances,
          :effective_date,
          :base_salary,
          :employee_id,
          :notes
        )
      end

      def move_url_ids_to_salary_package_params
        return unless params[:employee_id].present?
        params[:salary_package][:employee_id] = params[:employee_id].to_i
      end

      def own_resource?
        @salary_package&.employee&.id == current_employee.id
      end

      def consistent_resource?
        @salary_package&.employee_id == salary_package_params[:employee_id]
      end

      def authorize_create
        authorize!(:manage, :salary_package)
      end

      def authorize_read_all_or_own
          @collection = SalaryPackage.all
        # if can?(:read, :salary_package) || can?(:manage, :salary_package)
        #   @collection = SalaryPackage.all
        # elsif can?(:read_own, :salary_package)
        #   if params[:salary_package][:employee_id] == current_employee.id
        #     render_forbidden("You don't have permission to view this salary package")
        #     false
        #   end
        #   @collection = current_employee.salary_packages
        # else
        #   render_forbidden("You don't have permission to view salary packages")
        #   false
        # end
      end

      def authorize_read_specific
        if can?(:read, :salary_package)
          if !consistent_resource?
            serialize_errors({ detail: "Salary package does not belong to the specified employee" }, :unprocessable_entity)
            return false
          else
            return true
          end
        end

        return true if can?(:read_own, :salary_package) && own_resource?

        render_forbidden("You don't have permission to view this salary package")
        false
      end

      def authorize_manage
        unless can?(:manage, :salary_package)
          render_forbidden("You don't have permission to manage salary packages")
          false
        end
      end

      def render_forbidden(message)
        serialize_errors({ detail: message }, :forbidden)
      end
    end
  end
end
