module Api
  module Finance
    class SalaryCalculationsController < ApplicationController
      before_action :authenticate_session!
      before_action :move_url_ids_to_params
      before_action :set_employee, only: [ :index, :show, :download_slip, :update ]
      before_action :set_collection, only: [ :index ]
      before_action :set_salary_calculation, only: [ :show, :download_slip, :update ]
      before_action :authorize_index, only: [ :index ]
      before_action :authorize_show, only: [ :show ]
      before_action :authorize_download_slip, only: [ :download_slip ]
      before_action :authorize_calculate, only: [ :calculate_period, :calculate_custom ]
      before_action :authorize_update, only: [ :update ]

      api! "Lists salary calculations"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, desc: "ID of the employee"
      param_group :pagination_params
      param_group :filter_params
      param_group :sort_params
      param_group :include_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Lists salary calculations for an employee based on the user's permissions:
        — Users with <code>:read, :salary_calculation</code> permission can see all salary calculations
        — Users with <code>:read_own, :salary_calculation</code> permission can only see their own salary calculations

        Supports filtering by period, status, and payment_date.
        Supports sorting with sort=field or sort=-field (descending).
        Supports pagination with page[number] and page[size].
      HTML
      )
      returns code: 200, desc: "List of salary calculations"

      def index
        apply_filters(@collection) do |filtered_collection|
          records, meta = paginate(filtered_collection)
          serialize_response(records, meta: meta)
        end
      end

      api! "Shows a specific salary calculation"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary calculation"
      param :employee_id, Integer, desc: "ID of the employee"
      param :include_details, [ true, false ], desc: "Include detailed breakdown of the calculation"
      param_group :include_params
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Shows details for a specific salary calculation including gross salary, deductions, and net salary.
        — Users with <code>:read, :salary_calculation</code> permission can see any salary calculation
        — Users with <code>:read_own, :salary_calculation</code> permission can only see their own salary calculations

        When <code>include_details=true</code> is specified, the response will include a detailed breakdown
        of how the salary was calculated, including base salary, additions, and itemized deductions.
      HTML
      )
      returns code: 200, desc: "Salary calculation details"
      error code: 404, desc: "Salary calculation not found"

      def show
        # Check if details are requested
        include_details = params[:include_details].present? && params[:include_details] == 'true'

        serialize_response(@salary_calculation, include_details: include_details)
      end

      api! "Downloads a salary slip PDF"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary calculation"
      param :employee_id, Integer, desc: "ID of the employee"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Downloads the PDF salary slip for a specific salary calculation.
        — Users with <code>:read, :salary_calculation</code> permission can download any salary slip
        — Users with <code>:read_own, :salary_calculation</code> permission can only download their own salary slips

        Note: Salary slips are only available for paid salary calculations.
      HTML
      )
      returns code: 302, desc: "Redirect to the PDF file"
      error code: 404, desc: "Salary slip not found"

      def download_slip
        if @salary_calculation.salary_slip_pdf.attached?
          redirect_to rails_blob_url(@salary_calculation.salary_slip_pdf)
        else
          serialize_errors({ detail: "Salary slip not found" }, :not_found)
        end
      end

      def calculate_period
        period = params[:period]

        unless period =~ /\A\d{4}-\d{2}\z/
          return serialize_errors({ detail: "Period must be in format YYYY-MM" })
        end

        # Parse the period to get start_date and end_date
        year, month = period.split('-').map(&:to_i)
        start_date = Date.new(year, month, 1)
        end_date = start_date.end_of_month

        results = {
          success: [],
          failure: []
        }

        Employee.active.each do |employee|
          service = Salary::CalculationService.new(employee, {
            start_date: start_date,
            end_date: end_date
          })
          calculation = service.calculate

          if calculation
            results[:success] << { employee_id: employee.id, calculation_id: calculation.id }
          else
            results[:failure] << { employee_id: employee.id, errors: service.errors }
          end
        end

        render json: results
      end

      api! "Calculates salary for a custom period"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :employee_id, Integer, required: true, desc: "ID of the employee"
      param :start_date, Date, required: true, desc: "Start date of the calculation period (YYYY-MM-DD)"
      param :end_date, Date, required: true, desc: "End date of the calculation period (YYYY-MM-DD)"
      param :reason, String, desc: "Reason for custom calculation (e.g., 'termination')"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Calculates salary for a custom date range, useful for:
        - Employee termination
        - Mid-month adjustments
        - Partial period calculations

        This endpoint creates a day-specific salary calculation that pro-rates
        the salary based on the number of working days in the specified period.

        Requires permission: <code>:calculate, :salary_calculation</code>
      HTML
      )
      returns code: 200, desc: "Calculation result"
      error code: 422, desc: "Validation errors"

      # POST /api/finance/salary_calculations/calculate_custom
      def calculate_custom
        # Validate parameters
        employee_id = params[:employee_id]
        start_date = params[:start_date]
        end_date = params[:end_date]
        reason = params[:reason]

        begin
          start_date = Date.parse(start_date) if start_date.is_a?(String)
          end_date = Date.parse(end_date) if end_date.is_a?(String)
        rescue ArgumentError
          return serialize_errors({ detail: "Invalid date format. Use YYYY-MM-DD." })
        end

        # Find employee
        employee = Employee.find_by(id: employee_id)
        unless employee
          return serialize_errors({ detail: "Employee not found" }, :not_found)
        end

        # Calculate custom period salary
        service = Salary::CalculationService.new(
          employee,
          {
            start_date: start_date,
            end_date: end_date,
            reason: reason
          }
        )

        calculation = service.calculate

        if calculation
          serialize_response(calculation)
        else
          serialize_errors({ detail: service.errors.join(", ") })
        end
      end

      api! "Updates a salary calculation"
      header "Authorization", "Scoped session token as Bearer token", required: true
      param :id, Integer, required: true, desc: "ID of the salary calculation"
      param :employee_id, Integer, desc: "ID of the employee"
      param :status, String, desc: "Status of the salary calculation (e.g., 'submitted', 'approved', 'paid')"
      description self.jsonapi_with_docs(<<-HTML
        <b>Endpoint Details</b>

        Updates a salary calculation. This endpoint can be used to:
        - Submit a salary calculation for approval (change status from 'draft' to 'submitted')
        - Approve a salary calculation (change status from 'submitted' to 'approved')
        - Mark a salary calculation as paid (change status from 'approved' to 'paid')

        Required permissions:
        - <code>:submit, :salary_calculation</code> for submitting a calculation
        - <code>:update, :salary_calculation</code> for other updates
      HTML
      )
      returns code: 200, desc: "Updated salary calculation"
      error code: 422, desc: "Validation errors"

      def update
        # Set the actor_user_id for approval tracking
        @salary_calculation.actor_user_id = current_user.id

        if @salary_calculation.update(salary_calculation_params)
          serialize_response(@salary_calculation)
        else
          serialize_errors({ detail: @salary_calculation.errors.full_messages.join(", ") })
        end
      end

      private

      def move_url_ids_to_params
        return unless params[:employee_id].present?
        params[:salary_calculation] ||= {}
        params[:salary_calculation][:employee_id] = params[:employee_id].to_i
      end

      def set_collection
        @collection = if @employee
                        @employee.salary_calculations
                      else
                        SalaryCalculation.all
                      end
      end

      def set_employee
        employee_id = params[:employee_id] || params.dig(:salary_calculation, :employee_id)
        if employee_id
          @employee = Employee.find(employee_id)
        else
          @employee = current_employee
        end
      end

      def set_salary_calculation
        if @employee
          @salary_calculation = @employee.salary_calculations.find(params[:id])
        else
          @salary_calculation = SalaryCalculation.find(params[:id])
        end
      end

      def current_employee?
        @employee&.id == current_employee.id
      end

      def authorize_index
        return true if can?(:read, :salary_calculation)
        return true if current_employee? && can?(:read_own, :salary_calculation)

        render_forbidden("You don't have permission to view salary calculations for this employee")
      end

      def authorize_show
        return true if can?(:read, :salary_calculation)
        return true if current_employee? && can?(:read_own, :salary_calculation)

        render_forbidden("You don't have permission to view this salary calculation")
        false
      end

      def authorize_download_slip
        authorize_show
      end

      def authorize_calculate
        return true if can?(:calculate, :salary_calculation)

        render_forbidden("You don't have permission to calculate salaries")
        false
      end

      def authorize_update
        # Check if this is a status update to 'submitted'
        if params.dig(:salary_calculation, :status) == 'submitted'
          return true if can?(:submit, :salary_calculation)
          render_forbidden("You don't have permission to submit this salary calculation")
          return false
        end

        # For other updates
        return true if can?(:update, :salary_calculation)

        render_forbidden("You don't have permission to update this salary calculation")
        false
      end

      def salary_calculation_params
        params.require(:salary_calculation).permit(:status)
      end
    end
  end
end
