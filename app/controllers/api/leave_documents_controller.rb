module Api
  class LeaveDocumentsController < ApplicationController
    include Api::ActiveStorageFilterable
    before_action :authenticate_session!
    before_action :set_leave, only: [ :create, :index, :show, :update, :destroy ]
    before_action :set_document, only: [ :show, :update, :destroy ]
    before_action :authorize_read_all_or_own, only: [ :index ]
    before_action :authorize_read_specific, only: [ :show ]
    before_action :authorize_update, only: [ :create, :update, :destroy ]

    api! "Lists all documents for a leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :leave_id, Integer, required: true, desc: "ID of the leave"
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Lists all documents for the specified leave.
      Supports filtering by filename, content_type, and created_at.
      Supports sorting with sort=field or sort=-field (descending).
      Supports pagination with page[number] and page[size].
      Requires permission: <code>:read, :leave</code> or <code>:manage_own, :leave</code> for own leaves.
    HTML
    )
    returns code: 200, desc: "List of documents"

    def index
      apply_filters(@collection) do |filtered_documents|
        records, meta = paginate(filtered_documents)
        serialize_response(records, meta: meta)
      end
    end

    api! "Shows a specific document for a leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :leave_id, Integer, required: true, desc: "ID of the leave"
    param :id, Integer, required: true, desc: "ID of the document"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Shows details for a specific document.
      Requires permission: <code>:read, :leave</code> or <code>:manage_own, :leave</code> for own leaves.
    HTML
    )
    returns code: 200, desc: "Document details"
    error code: 404, desc: "Document not found"

    def show
      if @document
        serialize_response(@document)
      else
        serialize_errors({ detail: "Document not found" }, :not_found)
      end
    end

    api! "Uploads a document for a leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :leave_id, Integer, required: true, desc: "ID of the leave"
    param :document, ActionDispatch::Http::UploadedFile, required: true, desc: "File to attach to the leave"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Uploads and attaches a file to the specified leave.
      Requires permission: <code>:update, :leave</code> or <code>:manage_own, :leave</code> for own leaves.
    HTML
    )
    returns code: 201, desc: "Document uploaded successfully"
    error code: 422, desc: "No file provided or upload failed"

    def create
      uploaded_file = params.require(:document)

      if uploaded_file.present?
        # Use the direct attach method
        document = @leave.documents.attach(
          io: uploaded_file,
          filename: uploaded_file.original_filename,
          content_type: uploaded_file.content_type
        ).last

        # Return the response using the serializer
        serialize_response(document, status: :created)
      else
        serialize_errors({ detail: "No file provided." }, :unprocessable_entity)
      end
    end

    api! "Deletes a document from a leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :leave_id, Integer, required: true, desc: "ID of the leave"
    param :id, Integer, required: true, desc: "ID of the document to delete"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes a document from the specified leave.
      Requires permission: <code>:update, :leave</code> or <code>:manage_own, :leave</code> for own leaves.
    HTML
    )
    returns code: 204, desc: "Document deleted successfully"
    error code: 404, desc: "Document not found"

    def destroy
      if @document
        # Purge the attachment (this is the standard way to delete attachments in Active Storage)
        @document.purge

        # Return success response
        head :no_content
      else
        serialize_errors({ detail: "Document not found" }, :not_found)
      end
    end

    api! "Renames a document for a leave"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :leave_id, Integer, required: true, desc: "ID of the leave"
    param :id, Integer, required: true, desc: "ID of the document to rename"
    param :document, Hash, required: true, desc: "Document attributes" do
      param :filename, String, required: true, desc: "New filename for the document"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Renames an existing document for the specified leave without changing the file content.
      If only a base filename is provided without an extension, the original file extension will be preserved.
      Requires permission: <code>:update, :leave</code> or <code>:manage_own, :leave</code> for own leaves.
    HTML
    )
    returns code: 200, desc: "Document renamed successfully"
    error code: 404, desc: "Document not found"
    error code: 422, desc: "No filename provided or update failed"

    def update
      if @document.nil?
        return serialize_errors({ detail: "Document not found" }, :not_found)
      end

      # Get the new filename from nested parameters
      document_params = params.require(:document).permit(:filename)
      new_filename = document_params[:filename]

      if new_filename.present?
        # Get the blob associated with the document
        blob = @document.blob

        # Preserve the file extension if not provided in the new filename
        unless new_filename.include?(".")
          original_extension = File.extname(blob.filename.to_s)
          new_filename = "#{new_filename}#{original_extension}"
        end

        # Update the blob's filename
        if blob.update(filename: new_filename)
          # Return the updated document
          serialize_response(@document)
        else
          # Return errors if the update failed
          serialize_errors({ detail: "Failed to update filename." }, :unprocessable_entity)
        end
      else
        # No filename provided
        serialize_errors({ detail: "A new filename must be provided in document[filename]." }, :unprocessable_entity)
      end
    end

    private

    def set_leave
      @leave = Leave.find(params[:leave_id])
    rescue ActiveRecord::RecordNotFound
      serialize_errors({ detail: "Leave not found" }, :not_found)
      false # Return false to halt the action chain
    end

    def set_document
      @document = @leave.documents.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      @document = nil
    end

    def own_resource?
      @leave&.employee_id == current_user.id
    end

    def authorize_read_all_or_own
      if can?(:read, :leave)
        @collection = @leave.documents
      elsif can?(:manage_own, :leave) && own_resource?
        @collection = @leave.documents
      else
        render_forbidden("You don't have permission to view leave documents")
        false
      end
    end

    def authorize_read_specific
      unless can?(:read, :leave) ||
             (can?(:manage_own, :leave) && own_resource?)
        render_forbidden("You don't have permission to view this leave document")
        false
      end
    end

    def authorize_update
      unless can?(:update, :leave) ||
             (can?(:manage_own, :leave) && own_resource?)
        render_forbidden("You don't have permission to modify leave documents")
        false
      end
    end
  end
end
