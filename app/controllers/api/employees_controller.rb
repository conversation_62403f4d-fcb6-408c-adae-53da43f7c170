module Api
  class EmployeesController < ApplicationController

    before_action :authenticate_session!
    before_action :set_employee, only: %i[show update destroy]
    before_action :authorize_read_all_or_own, only: %i[index]
    before_action :authorize_read_specific, only: %i[show]
    before_action :authorize_create, only: %i[create]
    before_action :authorize_update, only: %i[update]
    before_action :authorize_destroy, only: %i[destroy]

    api! "Lists all employees"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param_group :pagination_params
    param_group :filter_params
    param_group :sort_params
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Returns a list of all employees.
      Supports filtering, sorting, and pagination.
      Requires permission: <code>:read, :employee</code>.
    HTML
    )
    returns code: 200, desc: "List of employees"

    def index
      apply_filters(@collection) do |filtered_and_sorted|
        records, meta = paginate(filtered_and_sorted)
        serialize_response(records.with_user_data, meta: meta)
      end
    end

    api! "Retrieves a specific employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the employee"
    param_group :include_params
    param_group :fields_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Fetches detailed information about a specific employee by ID.
      Requires permission: <code>:read, :employee</code>.
    HTML
    )
    returns code: 200, desc: "Employee details"

    def show
      # Serialize the employee with user attributes included
      serialize_response(@employee)
    end

    api! "Creates a new employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :employee, Hash, required: true, desc: "Employee details" do
      param :name, String, required: true, desc: "Full name of the employee"
      param :email, String, required: true, desc: "Email address of the employee"
      param :avatar, ActionDispatch::Http::UploadedFile, desc: "Employee profile picture"
      param :department, String, required: true, desc: "Department (admin, hr, finance, operations, it, programs, procurement)"
      param :start_date, Date, required: true, desc: "Employment start date"
      param :phone, String, required: true, desc: "Phone number"
      param :exempt_from_attendance_deductions, [true, false], desc: "Whether the employee is exempt from attendance deductions (default: false)"
      param :user_roles_list, Array, validator: EnhancedArrayValidator, desc: "Array of user roles to assign to the employee. Each role should include role_id and project_id (optional for global roles)."
      param "attachments[]", Array, of: ActionDispatch::Http::UploadedFile, desc: "Files to attach to the employee"
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new employee record.
      Supports uploading an avatar image for the employee.
      Supports attaching multiple files to the employee.
      Supports assigning user roles to the employee.
      Requires permission: <code>:create, :employee</code>.
    HTML
    )
    returns code: 201, desc: "Employee created successfully"
    error code: 422, desc: "Validation errors"

    def create
      @employee = Employee.new(create_params)

      if @employee.save
        serialize_response(@employee, status: :created, include: [ "user_roles" ])
      else
        serialize_errors(@employee.errors)
      end
    end

    api :PUT, "/employees/:id", "Updates an existing employee"
    api :PATCH, "/employees/:id", "Partially updates an employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the employee"
    param :employee, Hash, required: true, desc: "Updated employee fields" do
      param :name, String, desc: "Full name of the employee"
      param :email, String, desc: "Email address of the employee"
      param :avatar, ActionDispatch::Http::UploadedFile, desc: "Employee profile picture"
      param :department, String, desc: "Department (admin, hr, finance, operations, it, programs, procurement)"
      param :start_date, Date, desc: "Employment start date"
      param :phone, String, desc: "Phone number"
      param :exempt_from_attendance_deductions, [true, false], desc: "Whether the employee is exempt from attendance deductions"
      param :user_roles_list, Array, validator: EnhancedArrayValidator, desc: "Array of user roles to assign to the employee. Each role should include role_id and project_id (optional for global roles)."
      param "attachments[]", Array, of: ActionDispatch::Http::UploadedFile, desc: "Files to attach to the employee"
      # Note: Password cannot be updated through this endpoint
    end
    param_group :include_params
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing employee's information.
      Supports uploading a new avatar image for the employee.
      Supports attaching multiple files to the employee.
      Supports updating user roles for the employee.
      At least one field should be provided in the employee parameter.
      Requires permission: <code>:update, :employee</code>.
    HTML
    )
    returns code: 200, desc: "Employee updated successfully"
    error code: 422, desc: "Validation errors"

    def update
      if @employee.update(update_params)
        serialize_response(@employee.reload)
      else
        serialize_errors(@employee.errors)
      end
    end

    api! "Deletes an employee"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the employee"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Deletes an employee by ID.
      Requires permission: <code>:destroy, :employee</code>.
    HTML
    )
    returns code: 204, desc: "Employee deleted successfully"

    def destroy
      @employee.destroy!
      head :no_content
    end

    private

    def set_employee
      @employee = Employee.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      serialize_errors({ detail: "Employee not found" }, :not_found)
    end

    def create_params
      params.require(:employee).permit(
        :name,
        :email,
        :password,
        :avatar,
        :department,
        :start_date,
        :phone,
        :exempt_from_attendance_deductions,
        user_roles_list: [ :role_id, :project_id, :is_default ],
        attachments: []
      )
    end

    def update_params
      params.fetch(:employee, {}).permit(
        :name,
        :email,
        :avatar,
        :department,
        :start_date,
        :phone,
        :exempt_from_attendance_deductions,
        user_roles_list: [ :role_id, :project_id, :is_default ],
        attachments: []
      )
    end

    def authorize_read_all_or_own
      if can?(:read, :employee)
        @collection = Employee.all
      elsif can?(:read_own, :employee) && current_employee.present?
        @collection = Employee.where(id: current_employee.id)
      else
        render_forbidden("You don't have permission to view employees")
        false
      end
    end

    def authorize_read_specific
      unless can?(:read, :employee) ||
             (can?(:read_own, :employee) && is_own_resource?)
        render_forbidden("You don't have permission to view this employee")
        false
      end
    end

    def authorize_create
      authorize!(:create, :employee)
    end

    def authorize_update
      unless can?(:update, :employee) ||
             (can?(:update_own, :employee) && is_own_resource?)
        render_forbidden("You don't have permission to update this employee")
        false
      end
    end

    def authorize_destroy
      authorize!(:destroy, :employee)
    end

    def is_own_resource?
      @employee&.id == current_employee&.id
    end

    def render_forbidden(message)
      serialize_errors({ detail: message }, :forbidden)
    end
  end
end
