module Api
  class SettingsController < ApplicationController


    before_action :authenticate_session!
    before_action :set_setting, only: [:show, :update]

    api! "Lists settings"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :namespace, String, desc: "Filter by namespace"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Lists settings, optionally filtered by namespace.
      Requires permission: <code>:read, :setting</code>.
    HTML
    )
    returns code: 200, desc: "List of settings"

    def index
      return unless authorize!(:read, :setting)

      collection = if params[:namespace].present?
                     Setting.for_namespace(params[:namespace])
                   else
                     Setting.all
                   end

      apply_filters(collection) do |filtered_settings|
        records, meta = paginate(filtered_settings)
        serialize_response(records, meta: meta)
      end
    end

    api! "Shows a specific setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the setting"
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Shows details for a specific setting.
      Requires permission: <code>:read, :setting</code>.
    HTML
    )
    returns code: 200, desc: "Setting details"

    def show
      return unless authorize!(:read, :setting)

      if @setting
        serialize_response(@setting)
      else
        serialize_errors({ detail: "Setting not found" }, :not_found)
      end
    end

    api! "Creates a new setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :setting, Hash, required: true, desc: "Setting attributes" do
      param :namespace, String, required: true, desc: "Setting namespace"
      param :key, String, required: true, desc: "Setting key"
      param :value, String, required: true, desc: "Setting value"
      param :description, String, desc: "Setting description"
      param :is_editable, :boolean, desc: "Whether the setting can be edited"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Creates a new setting.
      Requires permission: <code>:create, :setting</code>.
    HTML
    )
    returns code: 201, desc: "Setting created successfully"

    def create
      return unless authorize!(:create, :setting)

      @setting = Setting.new(setting_params)

      if @setting.save
        serialize_response(@setting, status: :created)
      else
        serialize_errors(@setting.errors)
      end
    end

    api! "Updates a setting"
    header "Authorization", "Scoped session token as Bearer token", required: true
    param :id, Integer, required: true, desc: "ID of the setting"
    param :setting, Hash, required: true, desc: "Setting attributes" do
      param :value, String, desc: "Setting value"
      param :description, String, desc: "Setting description"
      param :is_editable, :boolean, desc: "Whether the setting can be edited"
    end
    description self.jsonapi_with_docs(<<-HTML
      <b>Endpoint Details</b>

      Updates an existing setting.
      Requires permission: <code>:update, :setting</code>.
      Note: Only editable settings can be updated.
    HTML
    )
    returns code: 200, desc: "Setting updated successfully"

    def update
      return unless authorize!(:update, :setting)

      unless @setting
        return serialize_errors({ detail: "Setting not found" }, :not_found)
      end

      unless @setting.is_editable
        return serialize_errors({ detail: "This setting cannot be edited" }, :unprocessable_entity)
      end

      if @setting.update(setting_update_params)
        # If we updated an attendance setting that affects period calculations, trigger recalculation
        if @setting.namespace == 'attendance' &&
           %w[work_start_time work_end_time duplicate_threshold_seconds].include?(@setting.key)
          # Queue a job to recalculate recent periods
          Attendance::BatchPeriodCalculationWorker.perform_async(
            30.days.ago.to_date.to_s,
            Date.today.to_s
          )
        end

        serialize_response(@setting)
      else
        serialize_errors(@setting.errors)
      end
    end

    private

    def set_setting
      @setting = Setting.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      @setting = nil
    end

    def setting_params
      params.require(:setting).permit(:namespace, :key, :value, :description, :is_editable)
    end

    def setting_update_params
      params.require(:setting).permit(:value, :description, :is_editable)
    end
  end
end
