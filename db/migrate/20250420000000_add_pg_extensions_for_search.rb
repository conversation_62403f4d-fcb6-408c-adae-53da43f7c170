class AddPgExtensionsForSearch < ActiveRecord::Migration[8.0]
  def up
    # Enable the pg_trgm extension for trigram similarity search
    execute "CREATE EXTENSION IF NOT EXISTS pg_trgm;"

    # Enable the unaccent extension for accent-insensitive search
    execute "CREATE EXTENSION IF NOT EXISTS unaccent;"

    # Add a GIN index for trigram search on the department column cast to text
    execute "CREATE INDEX employees_department_trgm_idx ON employees USING GIN (CAST(department AS text) gin_trgm_ops);"
  end

  def down
    # Remove the index
    execute "DROP INDEX IF EXISTS employees_department_trgm_idx;"

    # We don't remove the extensions as they might be used by other parts of the app
  end
end
