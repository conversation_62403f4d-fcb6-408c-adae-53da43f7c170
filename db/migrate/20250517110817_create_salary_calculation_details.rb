class CreateSalaryCalculationDetails < ActiveRecord::Migration[8.0]
  def change
    create_table :salary_calculation_details do |t|
      t.references :salary_calculation, null: false, foreign_key: true
      t.string :detail_type, null: false  # 'base', 'addition', 'deduction'
      t.string :category, null: false     # e.g., 'base_salary', 'social_security', 'tax', 'leave'
      t.decimal :amount, precision: 10, scale: 2, null: false
      t.text :description, null: false
      t.integer :reference_id
      t.string :reference_type

      t.timestamps
    end

    add_index :salary_calculation_details, [:detail_type, :category]
    add_index :salary_calculation_details, [:reference_type, :reference_id], name: 'index_salary_calculation_details_on_reference'
  end
end
