class RemoveStatusFromSalaryPackages < ActiveRecord::Migration[7.1]
  def up
    # First, ensure all packages have proper end dates
    execute <<-SQL
      UPDATE salary_packages
      SET end_date = (
        SELECT MIN(sp2.effective_date) - 1
        FROM salary_packages sp2
        WHERE sp2.employee_id = salary_packages.employee_id
          AND sp2.effective_date > salary_packages.effective_date
          AND sp2.id != salary_packages.id
      )
      WHERE status = 1 -- inactive
        AND end_date IS NULL;
    SQL

    remove_column :salary_packages, :status
    remove_index :salary_packages, :status, if_exists: true
  end

  def down
    add_column :salary_packages, :status, :integer, default: 0, null: false

    # Set status based on dates
    execute <<-SQL
      UPDATE salary_packages
      SET status = CASE
        WHEN end_date IS NULL OR end_date >= CURRENT_DATE THEN 0 -- active
        ELSE 1 -- inactive
      END;
    SQL

    add_index :salary_packages, :status
  end
end
