class UpdateAttendanceEventStructure < ActiveRecord::Migration[8.0]
  def up
    # Part 1: Update activity types to add 'regular' as the first option

    # Simpler approach: directly update the integer values
    # First, update check-in events to have activity_type = 0 (regular)
    execute <<-SQL
      UPDATE attendance_events SET activity_type = 0 WHERE event_type = 0;
    SQL

    # Then shift all other activity types up by 1
    execute <<-SQL
      UPDATE attendance_events SET activity_type = activity_type + 1 WHERE event_type != 0;
    SQL

    # Part 2: Check if timestamp conversion is needed

    # Check if the timestamp column is already an integer
    timestamp_type = connection.columns(:attendance_events).find { |c| c.name == 'timestamp' }.type

    # Only convert if it's not already an integer
    if timestamp_type != :integer
      # First, create a temporary column to store the integer values
      add_column :attendance_events, :timestamp_int, :integer

      # Convert existing datetime values to integer timestamps
      execute <<-SQL
        UPDATE attendance_events
        SET timestamp_int = EXTRACT(EPOCH FROM timestamp)::integer
      SQL

      # Remove the old timestamp column
      remove_column :attendance_events, :timestamp

      # Add the new timestamp column with the integer values
      add_column :attendance_events, :timestamp, :integer, null: false, default: 0

      # Copy the values from the temporary column
      execute <<-SQL
        UPDATE attendance_events
        SET timestamp = timestamp_int
      SQL

      # Remove the temporary column
      remove_column :attendance_events, :timestamp_int

      # Recreate the index
      add_index :attendance_events, [:employee_id, :timestamp]
    else
      puts "Timestamp column is already an integer, skipping conversion."
    end
  end

  def down
    # Part 1: Revert activity type changes

    # First, shift all non-regular activity types down by 1
    execute <<-SQL
      UPDATE attendance_events SET activity_type = activity_type - 1 WHERE activity_type > 0;
    SQL

    # Then set all check-in events back to break (which will be 0 after the shift)
    execute <<-SQL
      UPDATE attendance_events SET activity_type = 0 WHERE event_type = 0;
    SQL

    # Part 2: Check if timestamp conversion is needed

    # Check if the timestamp column is already a datetime
    timestamp_type = connection.columns(:attendance_events).find { |c| c.name == 'timestamp' }.type

    # Only convert if it's not already a datetime
    if timestamp_type != :datetime
      # First, create a temporary column to store the datetime values
      add_column :attendance_events, :timestamp_datetime, :datetime

      # Convert integer timestamps back to datetime values
      execute <<-SQL
        UPDATE attendance_events
        SET timestamp_datetime = to_timestamp(timestamp)
      SQL

      # Remove the old timestamp column
      remove_column :attendance_events, :timestamp

      # Add the new timestamp column with the datetime values
      add_column :attendance_events, :timestamp, :datetime, null: true

      # Copy the values from the temporary column
      execute <<-SQL
        UPDATE attendance_events
        SET timestamp = timestamp_datetime
      SQL

      # Set default values for any null timestamps
      execute <<-SQL
        UPDATE attendance_events
        SET timestamp = CURRENT_TIMESTAMP
        WHERE timestamp IS NULL
      SQL

      # Now make it not null
      change_column_null :attendance_events, :timestamp, false

      # Remove the temporary column
      remove_column :attendance_events, :timestamp_datetime

      # Recreate the index
      add_index :attendance_events, [:employee_id, :timestamp]
    else
      puts "Timestamp column is already a datetime, skipping conversion."
    end
  end
end
