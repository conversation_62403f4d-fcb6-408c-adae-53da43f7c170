class CreateSocialSecurityConfigs < ActiveRecord::Migration[8.0]
  def change
    create_table :social_security_configs do |t|
      t.decimal :employee_rate, precision: 5, scale: 2, null: false
      t.decimal :employer_rate, precision: 5, scale: 2, null: false
      t.decimal :max_salary, precision: 10, scale: 2
      t.date :effective_date, null: false
      t.date :end_date

      t.timestamps
    end

    add_index :social_security_configs, :effective_date
    add_index :social_security_configs, [:effective_date, :end_date]
  end
end
