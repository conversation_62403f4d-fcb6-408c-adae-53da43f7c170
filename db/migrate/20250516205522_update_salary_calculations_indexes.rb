class UpdateSalaryCalculationsIndexes < ActiveRecord::Migration[8.0]
  def change
    # We're not using custom_period anymore, instead we're using period_start_date and period_end_date
    # Remove the old index
    remove_index :salary_calculations, [ :employee_id, :period ], if_exists: true

    # Add a new index on employee_id, period_start_date, and period_end_date
    add_index :salary_calculations, [ :employee_id, :period_start_date, :period_end_date ],
             unique: true,
             name: 'index_salary_calculations_on_employee_and_dates'
  end
end
