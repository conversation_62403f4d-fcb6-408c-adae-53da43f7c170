class AddPhoneToEmployeeSearchViewsData < ActiveRecord::Migration[8.0]
  def up
    # Add phone column to the employee_search_views_data table
    execute <<-SQL
      ALTER TABLE employee_search_views_data ADD COLUMN phone text;
    S<PERSON>

    # Drop the existing view and dependent objects
    execute <<-SQL
      DROP VIEW IF EXISTS employee_search_views CASCADE;
    SQL

    # Recreate the view with the phone column
    execute <<-SQL
      CREATE VIEW employee_search_views AS
      SELECT
        esd.employee_id,
        esd.department,
        esd.start_date,
        esd.status,
        esd.user_id,
        esd.name,
        esd.email,
        esd.phone,
        esd.search_document
      FROM
        employee_search_views_data esd;
    SQL

    # Update the materialized view to include the phone column
    execute <<-SQL
      DROP MATERIALIZED VIEW IF EXISTS employee_search_data;

      CREATE MATERIALIZED VIEW employee_search_data AS
      SELECT
        esv.employee_id,
        esv.department,
        esv.start_date,
        esv.status,
        esv.user_id,
        esv.name,
        esv.email,
        esv.phone,
        esv.search_document,
        to_tsvector('english', COALESCE(esv.search_document, '')) AS search_vector
      FROM
        employee_search_views esv;
    SQL

    # Recreate the indexes
    execute <<-S<PERSON>
      CREATE INDEX employee_search_data_employee_id_idx ON employee_search_data (employee_id);
      CREATE INDEX employee_search_data_search_vector_idx ON employee_search_data USING GIN (search_vector);
    SQL
  end

  def down
    # Drop the materialized view
    execute <<-SQL
      DROP MATERIALIZED VIEW IF EXISTS employee_search_data;
    SQL

    # Drop the view and dependent objects
    execute <<-SQL
      DROP VIEW IF EXISTS employee_search_views CASCADE;
    SQL

    # Recreate the view without the phone column
    execute <<-SQL
      CREATE VIEW employee_search_views AS
      SELECT
        esd.employee_id,
        esd.department,
        esd.start_date,
        esd.status,
        esd.user_id,
        esd.name,
        esd.email,
        esd.search_document
      FROM
        employee_search_views_data esd;
    SQL

    # Recreate the materialized view without the phone column
    execute <<-SQL
      CREATE MATERIALIZED VIEW employee_search_data AS
      SELECT
        esv.employee_id,
        esv.department,
        esv.start_date,
        esv.status,
        esv.user_id,
        esv.name,
        esv.email,
        esv.search_document,
        to_tsvector('english', COALESCE(esv.search_document, '')) AS search_vector
      FROM
        employee_search_views esv;
    SQL

    # Recreate the indexes
    execute <<-SQL
      CREATE INDEX employee_search_data_employee_id_idx ON employee_search_data (employee_id);
      CREATE INDEX employee_search_data_search_vector_idx ON employee_search_data USING GIN (search_vector);
    SQL

    # Remove the phone column from the employee_search_views_data table
    execute <<-SQL
      ALTER TABLE employee_search_views_data DROP COLUMN IF EXISTS phone;
    SQL
  end
end
