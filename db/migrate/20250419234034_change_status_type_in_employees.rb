class ChangeStatusTypeInEmployees < ActiveRecord::Migration[8.0]
  def up
    # First, create a temporary column to hold the integer values
    add_column :employees, :status_int, :integer, default: 0

    # Update the temporary column based on the string values
    execute <<-SQL
      UPDATE employees
      SET status_int = CASE
        WHEN status = 'active' THEN 0
        WHEN status = 'inactive' THEN 1
        ELSE 0
      END
    SQL

    # Remove the string column
    remove_column :employees, :status

    # Add the integer column with the same name
    add_column :employees, :status, :integer, default: 0

    # Copy data from the temporary column
    execute <<-SQL
      UPDATE employees
      SET status = status_int
    SQL

    # Remove the temporary column
    remove_column :employees, :status_int
  end

  def down
    # First, create a temporary column to hold the string values
    add_column :employees, :status_str, :string, default: 'active'

    # Update the temporary column based on the integer values
    execute <<-SQL
      UPDATE employees
      SET status_str = CASE
        WHEN status = 0 THEN 'active'
        WHEN status = 1 THEN 'inactive'
        ELSE 'active'
      END
    SQL

    # Remove the integer column
    remove_column :employees, :status

    # Add the string column with the same name
    add_column :employees, :status, :string, default: 'active'

    # Copy data from the temporary column
    execute <<-SQL
      UPDATE employees
      SET status = status_str
    SQL

    # Remove the temporary column
    remove_column :employees, :status_str
  end
end
