class CreateEmployeeSearchViews < ActiveRecord::Migration[8.0]
  # Helper method to handle status values in the migration
  # This handles various formats of status that might be encountered in different environments
  def handle_status_for_migration(status)
    if status.nil? || status.to_s.strip.empty?
      # Nil or empty string should be NULL
      'NULL'
    elsif status.is_a?(Integer)
      # Integer values can be used directly
      status.to_s
    elsif status.is_a?(String) && status.to_i.to_s == status
      # If it's a string that can be converted to an integer, use the integer value
      status.to_i.to_s
    elsif status.is_a?(String) && status.to_s == 'active'
      # Handle 'active' string specifically (maps to 0 in the enum)
      '0'
    elsif status.is_a?(String) && status.to_s == 'inactive'
      # Handle 'inactive' string specifically (maps to 1 in the enum)
      '1'
    elsif status.is_a?(String) && status.to_s == 'pending'
      # Handle 'pending' string specifically (maps to 2 in the enum)
      '2'
    else
      # For any other case, use NULL
      'NULL'
    end
  end

  def up
    # Create the backing table for the view with employee_id as primary key
    execute <<-SQL
      CREATE TABLE employee_search_views_data (
        employee_id bigint PRIMARY KEY,
        department varchar,
        start_date date,
        status integer,
        user_id bigint,
        name varchar,
        email varchar,
        search_document text
      )
    SQL

    # Create the base view using Scenic
    create_view :employee_search_views

    # Create a materialized view for search with additional columns
    execute <<-SQL
      CREATE MATERIALIZED VIEW employee_search_data AS
      SELECT
        esv.employee_id,
        esv.department,
        esv.start_date,
        esv.status,
        esv.user_id,
        esv.name,
        esv.email,
        esv.search_document,
        to_tsvector('english', COALESCE(esv.search_document, '')) AS search_vector
      FROM
        employee_search_views esv;
    SQL

    # Add indexes for searching
    execute <<-SQL
      CREATE INDEX employee_search_data_employee_id_idx ON employee_search_data (employee_id);
      CREATE INDEX employee_search_data_search_vector_idx ON employee_search_data USING GIN (search_vector);
    SQL

    # Populate the backing table with initial data
    Employee.find_each do |employee|
      # Get user data if available
      name = nil
      email = nil

      if employee.respond_to?(:user_data) && employee.user_data.present?
        name = employee.name
        email = employee.email
      end

      # Create search document
      search_document = [
        employee.department.to_s,
        name.to_s,
        email.to_s
      ].join(' ')

      # Insert into backing table
      execute <<-SQL
        INSERT INTO employee_search_views_data (
          employee_id, department, start_date, status, user_id, name, email, search_document
        ) VALUES (
          #{employee.id},
          #{connection.quote(employee.department.to_s)},
          #{connection.quote(employee.start_date)},
          #{handle_status_for_migration(employee.status)},
          #{employee.user_id.nil? || employee.user_id.to_s.strip.empty? ? 'NULL' : employee.user_id},
          #{connection.quote(name)},
          #{connection.quote(email)},
          #{connection.quote(search_document)}
        )
      SQL
    end
  end

  def down
    # Drop the materialized view and indexes
    execute <<-SQL
      DROP MATERIALIZED VIEW IF EXISTS employee_search_data;
    SQL

    # Drop the base view
    drop_view :employee_search_views

    # Drop the backing table
    execute "DROP TABLE IF EXISTS employee_search_views_data;"
  end
end
