#!/bin/bash

# ====== CONFIGURATION ======
DROPLET_USER="athar"
DROPLET_IP="**************"
CONTEXT_NAME="athar-droplet"

# ====== CHECK SSH ACCESS ======
echo "🔐 Checking SSH access to $DROPLET_USER@$DROPLET_IP..."
ssh -o BatchMode=yes "$DROPLET_USER@$DROPLET_IP" "echo SSH access OK" || {
    echo "❌ SSH failed. Make sure your SSH key is set up correctly."
    exit 1
}

# ====== CREATE DOCKER CONTEXT ======
echo "🐳 Creating Docker context: $CONTEXT_NAME..."
docker context create "$CONTEXT_NAME" \
  --docker "host=ssh://$DROPLET_USER@$DROPLET_IP" || {
    echo "❌ Failed to create Docker context."
    exit 1
}

# ====== SWITCH TO NEW CONTEXT ======
echo "🔄 Switching Docker to use context: $CONTEXT_NAME"
docker context use "$CONTEXT_NAME" || {
    echo "❌ Failed to switch context."
    exit 1
}

# ====== TEST CONNECTION ======
echo "✅ Connected! Running 'docker ps' on $DROPLET_IP:"
docker ps