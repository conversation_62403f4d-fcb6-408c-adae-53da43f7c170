# Device Commands System Design

Based on the existing attendance device system and the requirements to support various device commands through the API, I propose the following system design:

## 1. Command Architecture Overview

### Core Components:
1. **Command Interface in Adapters**: Each adapter declares supported commands
2. **Command Executor**: Service to execute commands on devices
3. **API Endpoints**: New endpoints to list and execute commands
4. **Command Results**: Standardized response format
5. **Command Audit**: Logging of command executions

## 2. Data Model Changes

### New Models:

#### `Attendance::CommandResult` (ActiveStruct)
```ruby
module Attendance
  class CommandResult < Athar::Commons::ActiveStruct::Base
    attribute :success, :boolean, default: false
    attribute :message, :string
    attribute :error, :string
    attribute :details, :hash, default: {}
    
    # Helper methods for creating standard responses
    def self.success(message = nil, details = {})
      new(success: true, message: message, details: details)
    end
    
    def self.failure(error = nil, details = {})
      new(success: false, error: error, details: details)
    end
  end
end
```

#### `Attendance::CommandExecution` (Audit Log)
```ruby
class Attendance::CommandExecution < ApplicationRecord
  # Attributes:
  # - device_id: references Attendance::Device
  # - command_name: string
  # - parameters: jsonb
  # - status: string (enum: "running", "completed", "failed")
  # - result: jsonb
  # - executed_by_id: references Employee
  # - started_at: datetime
  # - completed_at: datetime
  
  # Associations:
  belongs_to :device, class_name: 'Attendance::Device'
  belongs_to :executed_by, class_name: 'Employee', optional: true
  
  # Scopes, validations, etc.
  validates :command_name, presence: true
  
  scope :recent, -> { order(created_at: :desc).limit(20) }
end
```

## 3. Adapter Enhancement

Extend the `BaseAdapter` class with command capabilities and execution interface:

```ruby
# In app/services/attendance/adapters/base_adapter.rb

# Declare command support methods
def supports_commands?
  false
end

def available_commands
  []
end

# Command execution interface
def execute_command(command_name, parameters = {})
  unless supports_commands?
    return Attendance::CommandResult.failure("Device does not support commands")
  end
  
  unless available_commands.include?(command_name.to_s)
    return Attendance::CommandResult.failure("Command '#{command_name}' not supported by this device")
  end
  
  method_name = "execute_#{command_name}"
  if respond_to?(method_name, true)
    send(method_name, parameters)
  else
    Attendance::CommandResult.failure("Command '#{command_name}' not implemented for #{self.class.name}")
  end
end
```

### ZKTeco Adapter Implementation:

```ruby
# In app/services/attendance/adapters/zkteco_adapter.rb

# Override base adapter methods
def supports_commands?
  true
end

def available_commands
  %w[
    restart test_voice clear_logs clear_lcd write_lcd
    unlock door_state poweroff refresh
  ]
end

# Command implementations
def execute_restart(params = {})
  log_info("Executing restart command on device")
  
  with_connection do |zk_client|
    # Use the restart method from the rbzk gem
    result = zk_client.restart
    log_info("Device restart command sent successfully")
    Attendance::CommandResult.success("Device restarting")
  end
rescue => e
  log_error("Error restarting device", e)
  Attendance::CommandResult.failure(e.message)
end

def execute_test_voice(params = {})
  log_info("Executing test voice command on device")
  
  with_connection do |zk_client|
    # Use the test_voice method from the rbzk gem
    result = zk_client.test_voice
    log_info("Voice test initiated successfully")
    Attendance::CommandResult.success("Voice test initiated")
  end
rescue => e
  log_error("Error testing voice", e)
  Attendance::CommandResult.failure(e.message)
end

def execute_clear_logs(params = {})
  log_info("Executing clear logs command on device")
  
  with_connection do |zk_client|
    # Use the clear_attendance method from the rbzk gem
    # Note: The comment in the existing code suggests this might not be directly implemented
    # We may need to use a different approach or extend the gem
    if zk_client.respond_to?(:clear_data)
      result = zk_client.clear_data
      log_info("Logs cleared successfully")
      Attendance::CommandResult.success("Logs cleared successfully")
    else
      log_warn("Clear logs command not supported by the current rbzk version")
      Attendance::CommandResult.failure("Operation not supported by the current device library")
    end
  end
rescue => e
  log_error("Error clearing logs", e)
  Attendance::CommandResult.failure(e.message)
end

# Other command implementations...
```

### GenericHTTP Adapter Implementation:

```ruby
# In app/services/attendance/adapters/generic_http_adapter.rb

# Override base adapter methods
def supports_commands?
  true
end

def available_commands
  # HTTP adapter may support a different set of commands
  %w[restart unlock refresh]
end

# Command implementations specific to HTTP API
def execute_restart(params = {})
  log_info("Executing restart command via HTTP API")
  
  # Using the hypothetical HTTP API client
  response = http_client.post_command('restart')
  
  if response && response[:success]
    log_info("Restart command sent successfully")
    Attendance::CommandResult.success(response[:message] || "Device restarting")
  else
    log_error("Failed to send restart command: #{response&.dig(:error)}")
    Attendance::CommandResult.failure(response&.dig(:error) || "Unknown error")
  end
rescue => e
  log_error("Error sending restart command", e)
  Attendance::CommandResult.failure(e.message)
end

# Other command implementations...
```

## 4. Command Execution Service

```ruby
# app/services/attendance/command_executor.rb
module Attendance
  class CommandExecutor
    def self.execute(device, command_name, parameters = {}, employee = nil)
      # Create execution record
      execution = CommandExecution.create!(
        device: device,
        command_name: command_name,
        parameters: parameters,
        status: :running,
        executed_by: employee,
        started_at: Time.current
      )
      
      begin
        # Execute command on adapter
        adapter = device.create_adapter
        
        # Check if adapter supports commands and the specific command
        unless adapter.supports_commands?
          result = Attendance::CommandResult.failure("Device does not support commands")
          execution.update!(
            status: :failed, 
            result: result.as_json,
            completed_at: Time.current
          )
          return result
        end
        
        unless adapter.available_commands.include?(command_name.to_s)
          result = Attendance::CommandResult.failure("Command '#{command_name}' not supported")
          execution.update!(
            status: :failed, 
            result: result.as_json,
            completed_at: Time.current
          )
          return result
        end
        
        # Execute command
        result = adapter.execute_command(command_name, parameters)
        
        # Update execution record
        execution.update!(
          status: result.success ? :completed : :failed,
          result: result.as_json,
          completed_at: Time.current
        )
        
        result
      rescue => e
        result = Attendance::CommandResult.failure(e.message)
        execution.update!(
          status: :failed,
          result: result.as_json,
          completed_at: Time.current
        )
        
        result
      end
    end
  end
end
```

## 6. API Controller Endpoints

```ruby
# In app/controllers/api/attendance/devices_controller.rb

# List available commands
def available_commands
  authorize!(:execute_commands, :attendance_device)
  
  adapter = @attendance_device.create_adapter
  
  if adapter.supports_commands?
    commands = adapter.available_commands.map do |cmd|
      {
        name: cmd,
        display_name: cmd.titleize
      }
    end
    serialize_response(commands, serializer: CommandSerializer)
  else
    render json: { commands: [] }, status: :ok
  end
end

# Execute a command
def execute_command
  authorize!(:execute_commands, :attendance_device)
  
  command_name = params.require(:command)
  command_params = params.fetch(:parameters, {}).permit!.to_h
  
  adapter = @attendance_device.create_adapter
  
  # Check if device supports commands
  unless adapter.supports_commands?
    result = Attendance::CommandResult.failure("Device does not support commands")
    return render json: result, status: :unprocessable_entity
  end
  
  # Check if command is supported
  unless adapter.available_commands.include?(command_name)
    result = Attendance::CommandResult.failure("Command not supported")
    return render json: result, status: :unprocessable_entity
  end
  
  # Execute command
  result = Attendance::CommandExecutor.execute(@attendance_device, command_name, command_params, current_employee)
  
  if result.success
    render json: result, status: :ok
  else
    render json: result, status: :unprocessable_entity
  end
end

# Get command execution history
def command_history
  authorize!(:read, :attendance_device)
  
  executions = @attendance_device.command_executions.recent
  
  render json: {
    device_id: @attendance_device.id,
    device_name: @attendance_device.name,
    executions: executions.map do |exec|
      {
        id: exec.id,
        command: exec.command_name,
        status: exec.status,
        executed_at: exec.started_at,
        completed_at: exec.completed_at,
        result: exec.result
      }
    end
  }
end
```

## 7. Routes Configuration

```ruby
# In config/routes.rb
namespace :api do
  namespace :attendance do
    resources :devices do
      member do
        get :available_commands
        post :execute_command
        get :command_history
      end
    end
  end
end
```

## 8. Authorization Updates

```ruby
# In app/models/ability.rb or wherever permissions are defined
can :execute_commands, :attendance_device if employee.has_permission?(:attendance_device_commands)
```

## 9. Frontend Integration

The frontend would need:
1. A command panel in the device detail view
2. A dropdown to select available commands
3. Parameter input fields based on command requirements
4. Status indicators for command execution
5. Command history display

## 10. Implementation Plan

### 📋 MODIFICATION PLAN: DEVICE COMMANDS SYSTEM INTEGRATION

#### 🔍 CURRENT STATE ANALYSIS:
##### ✅ WHAT WE HAVE:
- Device Users Controller - Fully standardized with ActiveStruct
- Devices Controller - Complete CRUD + sync operations
- ZKTeco Adapter - User management methods implemented
- Base Adapter - User management interface defined
- Routes - Device users management routes configured

##### ❌ WHAT'S MISSING:
- Command support methods in adapters
- Command execution endpoints in devices controller
- Command execution model for audit
- Command executor service
- Command methods in adapters
- Command routes configuration
- Command authorization permissions

#### 🎯 IMPLEMENTATION PLAN:
##### Phase 1: Database & Models (Foundation)
- Create CommandResult ActiveStruct Model
- Create Command Execution Model
- Create Migrations

##### Phase 2: Service Layer (Business Logic)
- Extend Base Adapter with Command Interface
- Implement Commands in Specific Adapters
- Create Command Executor Service

##### Phase 3: API Layer (Controller & Routes)
- Add Command Endpoints to Devices Controller
- Add Command Routes
- Add Authorization

##### Phase 4: Standardization (Follow Patterns)
- Create Command Execution Serializer
- Use serialize_response Pattern

#### 🎯 INTEGRATION POINTS:
##### With Current Device Users System:
✅ Same controller (devices_controller.rb)  
✅ Same authorization patterns  
✅ Same serialization patterns (serialize_response)  
✅ Same route structure (nested under devices)

##### With Current Adapter System:
✅ Extends existing adapters (base + zkteco)  
✅ Uses existing connection methods (with_connection)  
✅ Follows existing logging patterns
✅ Follows capability-flag pattern (supports_x?)

#### 📊 EXPECTED OUTCOME:
- GET /api/attendance/devices/:id/available_commands
- POST /api/attendance/devices/:id/execute_command
- GET /api/attendance/devices/:id/command_history    # ⭐ TO ADD

#### 🚀 BENEFITS:
✅ Complete Device Management - Users + Sync + Commands  
✅ Consistent Patterns - Same standardization as device users  
✅ Audit Trail - Full command execution history  
✅ Authorization - Proper permission controls  
✅ Extensible - Easy to add new commands in specific adapters
✅ Adapter-Specific - Each adapter declares its own supported commands
✅ Synchronous - Simple, direct command execution
✅ Simplified Architecture - No background processing required
✅ Flexible - No database schema changes needed when adding new commands
✅ Type Safety - Using ActiveStruct for command results
✅ Standardized Responses - Consistent result format from all commands
